# Project Configuration Files Summary

This document provides a summary of various ScriptableObject configuration files used in the project.

## 1. AimingConfiguration.cs

Configuration for the aiming system. Located at `Assets/Scripts/Module/Aim/AimingConfiguration.cs`.

| Field Name                      | Type           | Default Value        | Description                                                              |
|---------------------------------|----------------|----------------------|--------------------------------------------------------------------------|
| **FOV Parameters**              |                |                      |                                                                          |
| `coneAngleBeforeAiming`         | `float`        | `45f`                | Angle of the detection cone in degrees when not actively aiming.         |
| `coneAngleAfterAiming`          | `float`        | `45f`                | Angle of the detection cone in degrees when actively aiming.             |
| `coneRange`                     | `float`        | `10f`                | Range of the detection cone.                                             |
| `detectionRadius`               | `float`        | `6f`                 | Radius of the detection sphere.                                          |
| **Target Selection**            |                |                      |                                                                          |
| `allowNonFOVTargets`            | `bool`         | `false`              | Whether to allow targeting enemies outside the player's field of view.   |
| `baseDistanceThreshold`         | `float`        | `10f`                | Base distance for normalization in target selection.                     |
| `baseAngleThreshold`            | `float`        | `45f`                | Base angle for normalization in target selection.                        |
| `distanceWeight`                | `float`        | `0.7f`               | Weight for distance in scoring (0-1).                                    |
| `angleWeight`                   | `float`        | `0.3f`               | Weight for angle in scoring (0-1).                                       |
| `targetSwitchHysteresis`        | `float`        | `0.15f`              | Prevents rapid switching between close targets (0-1).                    |
| **Target Switching Parameters** |                |                      |                                                                          |
| `TargetSwitchDistanceThreshold` | `float`        | `2.0f`               | Distance threshold for switching targets.                                |
| `MinTargetSwitchDistance`       | `float`        | `1.0f`               | Minimum distance required to allow target switching.                     |
| `TargetSwitchAngleThreshold`    | `float`        | `25.0f`              | Angle threshold for switching targets (degrees).                         |
| `TargetSwitchScoreThreshold`    | `float`        | `0.5f`               | Score threshold for switching targets.                                   |
| **AAA Aim Assist**              |                |                      |                                                                          |
| `dynamicFOVExpansion`           | `float`        | `15f`                | Degrees to temporarily expand FOV when a target is near the edge.        |
| `stickyAngle`                   | `float`        | `10f`                | Angle in degrees for sticky targeting.                                   |
| `stickyDistance`                | `float`        | `2.5f`               | Distance in meters for sticky targeting.                                 |
| `aimAssistSpeed`                | `float`        | `120f`               | Degrees per second for auto-rotation toward target.                      |
| `targetInterpolationSpeed`      | `float`        | `0.15f`              | How quickly to interpolate to a new target (0.01-1).                     |
| **Cooldown Settings**           |                |                      |                                                                          |
| `CooldownAfterLostAllEnenies`   | `float`        | `1.0f`               | Time (sec) to wait before aiming again after losing all enemies.         |
| **Ragdoll Aiming Controller Settings** |         |                      |                                                                          |
| `targetSwitchSmoothTime`        | `float`        | `0.3f`               | Smooth time for target switching.                                        |
| `weightSmoothTime`              | `float`        | `0.3f`               | Smooth time for weight changes.                                          |
| `maxRadiansDelta`               | `float`        | `3f`                 | Maximum radians delta for smooth rotation.                               |
| `maxMagnitudeDelta`             | `float`        | `3f`                 | Maximum magnitude delta for smooth rotation.                             |
| `slerpSpeed`                    | `float`        | `3f`                 | Slerp speed for rotation.                                                |
| `smoothDampTime`                | `float`        | `0f`                 | Smooth damp time.                                                        |
| `minDistance`                   | `float`        | `1f`                 | Minimum distance for aiming.                                             |
| `maxRootAngle`                  | `float`        | `45f`                | Maximum root angle for aiming.                                           |
| `turnToTargetTime`              | `float`        | `0.2f`               | Turn to target time.                                                     |
| **ECS Detection Default Values**|                |                      |                                                                          |
| `ecsDetectionRadius`            | `float`        | `6f`                 | Default detection radius for ECS systems.                                |
| `ecsDetectionRange`             | `float`        | `20f`                | Default detection range for ECS systems.                                 |
| `ecsDetectionAngle`             | `float`        | `45f`                | Default detection angle for ECS systems.                                 |
| `detectionCooldownTime`         | `float`        | `1.0f`               | Cooldown time for detection.                                             |
| **Speed Controller Settings**   |                |                      |                                                                          |
| `aimingSpeedCurve`              | `AnimationCurve` | Linear(0,0.8 to 1,0.6)  | Animation curve for aiming speed modifiers.                              |
| `strafingSpeedCurve`            | `AnimationCurve` | Linear(0,1.0 to 1,0.85) | Animation curve for strafing speed modifiers.                            |
| `backpedalSpeedCurve`           | `AnimationCurve` | Linear(0,1.0 to 1,0.7)  | Animation curve for backpedal speed modifiers.                           |
| `targetDistanceSpeedCurve`      | `AnimationCurve` | EaseInOut(0,0.3 to 10,1.0)| Animation curve for target distance speed control.                       |
| `speedMinTargetDistance`        | `float`        | `1.0f`               | Minimum target distance for speed calculations.                          |
| `speedMaxTargetDistance`        | `float`        | `10.0f`              | Maximum target distance for speed calculations.                          |
| `forwardThreshold`              | `float`        | `0.7f`               | Forward movement threshold.                                              |
| `backwardThreshold`             | `float`        | `-0.3f`              | Backward movement threshold.                                             |
| `strafeThreshold`               | `float`        | `0.7f`               | Strafe movement threshold.                                               |
| **Debug**                       |                |                      |                                                                          |
| `showTargetDebug`               | `bool`         | `false`              | Show debug information about target selection.                           |

## 2. MovementModuleConfiguration.cs

Configuration for the movement system. Located at `Assets/Scripts/Module/MovementModuleConfiguration.cs`.

| Field Name                      | Type           | Default Value        | Description                                                              |
|---------------------------------|----------------|----------------------|--------------------------------------------------------------------------|
| **Movement Improvement**        |                |                      |                                                                          |
| `improvedMovement`              | `bool`         | `false`              | Enable improved movement for smoother direction changes.                 |
| `useRootMotionForTurning`       | `bool`         | `false`              | Use root motion for turning animations.                                  |
| **Movement Parameters**         |                |                      |                                                                          |
| `accelerationTime`              | `float`        | `0.1f`               | Time to reach full speed.                                                |
| `decelerationTime`              | `float`        | `0.2f`               | Time to stop from full speed.                                            |
| `aimingSpeedMultiplier`         | `float`        | `0.8f`               | Slower movement while aiming (0.1-1).                                    |
| `strafingSpeedMultiplier`       | `float`        | `0.9f`               | Slightly slower when strafing sideways (0.1-1).                          |
| `backpedalSpeedMultiplier`      | `float`        | `0.7f`               | Slower when moving backward (0.1-1).                                     |
| `directionChangeCurve`          | `AnimationCurve` | EaseInOut(0,0 to 1,1)   | Smooth direction changes.                                                |
| **Speed and Rotation**          |                |                      |                                                                          |
| `moveSpeed`                     | `float`        | `5.0f`               | Base movement speed.                                                     |
| `rotationSpeed`                 | `float`        | `720.0f`             | Rotation speed in degrees per second.                                    |
| **Target Rotation Parameters**  |                |                      |                                                                          |
| `targetRotationSpeed`           | `float`        | `5f`                 | Speed for whole body rotation.                                           |
| `minRotationAngle`              | `float`        | `30f`                | Minimum angle difference to trigger whole body rotation.                 |
| `useWholeBodyRotation`          | `bool`         | `true`               | Toggle for whole body rotation.                                          |
| `rotationSmoothTime`            | `float`        | `0.2f`               | Smoothing time for rotation.                                             |
| `maxRotationSpeed`              | `float`        | `180f`               | Maximum rotation speed (deg/s) for in-place rotation.                  |
| **In-Place Rotation**           |                |                      |                                                                          |
| `useInPlaceRotation`            | `bool`         | `true`               | Enable in-place rotation.                                                |
| `rotationCooldown`              | `float`        | `0.2f`               | Cooldown between rotation attempts.                                      |
| `rotationStabilityThreshold`    | `float`        | `0.5f`               | Time to wait before considering rotation stable.                         |
| **Transition Parameters**       |                |                      |                                                                          |
| `fastRotationSpeed`             | `float`        | `0.2f`               | Duration for fast rotation tween.                                        |
| `waitForStop`                   | `float`        | `0.5f`               | Wait time for stop state.                                                |
| `transitionCooldown`            | `float`        | `0.25f`              | Cooldown between state transitions.                                      |
| `improvedTransitionCooldown`    | `float`        | `0.1f`               | Reduced cooldown for improved movement.                                  |

## 3. AnimationSystemConfiguration.cs

Main animation system configuration that centralizes all animation settings. Located at `Assets/Scripts/ScriptableObjects/AnimationSystemConfiguration.cs`.

| Field Name                      | Type                        | Default Value        | Description                                                              |
|---------------------------------|-----------------------------|----------------------|--------------------------------------------------------------------------|
| **System Settings**             |                             |                      |                                                                          |
| `useAnimancerByDefault`         | `bool`                      | `true`               | Whether to use Animancer by default or Animator Controller.              |
| `enableDebugLogging`            | `bool`                      | `false`              | Enable debug logging for animation system.                               |
| `defaultTransitionDuration`     | `float`                     | `0.25f`              | Default transition duration for animations (0.05-2s).                    |
| `updateRate`                    | `float`                     | `30f`                | Animation update rate in FPS (15-60).                                    |
| **Transition Configurations**   |                             |                      |                                                                          |
| `transitionConfig`              | `TransitionConfiguration`   | *(object)*           | Transition settings for different priority levels. (See 3.1)             |
| **State Mappings**              |                             |                      |                                                                          |
| `movementStateMappings`         | `List<MovementStateMapping>`| *(empty list)*      | Movement state animation mappings. (See 3.5)                             |
| `weaponStateMappings`           | `List<WeaponStateMapping>`  | *(empty list)*      | Weapon state animation mappings. (See 3.6)                               |
| **Performance Settings**        |                             |                      |                                                                          |
| `performanceConfig`             | `PerformanceConfiguration`  | *(object)*           | Performance optimization settings. (See 3.2)                             |
| **LOD Settings**                |                             |                      |                                                                          |
| `lodConfig`                     | `LODConfiguration`          | *(object)*           | Level of Detail settings for animations. (See 3.3)                       |
| **Debug Settings**              |                             |                      |                                                                          |
| `debugConfig`                   | `DebugConfiguration`        | *(object)*           | Debug and visualization settings. (See 3.4)                              |

### 3.1. Nested: TransitionConfiguration

Configuration for animation transitions.

| Field Name                      | Type           | Default Value        | Description                                                              |
|---------------------------------|----------------|----------------------|--------------------------------------------------------------------------|
| `criticalDuration`              | `float`        | `0.1f`               | Duration for critical priority transitions (0.05-1s).                    |
| `highDuration`                  | `float`        | `0.15f`              | Duration for high priority transitions (0.1-1s).                         |
| `mediumDuration`                | `float`        | `0.25f`              | Duration for medium priority transitions (0.15-1s).                      |
| `lowDuration`                   | `float`        | `0.35f`              | Duration for low priority transitions (0.2-2s).                          |
| `transitionCurve`               | `AnimationCurve` | EaseInOut(0,0 to 1,1)   | Easing curve for transitions.                                            |
| `enableSmoothBlending`          | `bool`         | `true`               | Enable smooth transition blending.                                       |

### 3.2. Nested: PerformanceConfiguration

Performance optimization configuration.

| Field Name                      | Type           | Default Value        | Description                                                              |
|---------------------------------|----------------|----------------------|--------------------------------------------------------------------------|
| `enableBatchedUpdates`          | `bool`         | `true`               | Enable batched animation updates.                                        |
| `maxAnimationsPerFrame`         | `int`          | `10`                 | Maximum animations to update per frame (1-50).                           |
| `enableCulling`                 | `bool`         | `true`               | Enable animation culling when off-screen.                                |
| `enableStatePooling`            | `bool`         | `true`               | Enable animation state pooling.                                          |
| `maxPooledStates`               | `int`          | `50`                 | Maximum pooled states (10-100).                                          |
| `enableGCOptimization`          | `bool`         | `true`               | Enable garbage collection optimization.                                  |

### 3.3. Nested: LODConfiguration

Level of Detail configuration for animations.

| Field Name                      | Type           | Default Value        | Description                                                              |
|---------------------------------|----------------|----------------------|--------------------------------------------------------------------------|
| `enableLOD`                     | `bool`         | `true`               | Enable Level of Detail system.                                           |
| `lodDistance`                   | `float`        | `50f`                | Distance at which LOD starts applying (10-100).                          |
| `lodLevels`                     | `List<LODLevel>`| Specific list      | LOD levels and their distance/update rate thresholds. (See 3.7)          |
|                                 |                | `L0: {0, 25f, 60f}`  | Default Level 0: MaxDist 25f, UpdateRate 60fps                           |
|                                 |                | `L1: {1, 50f, 30f}`  | Default Level 1: MaxDist 50f, UpdateRate 30fps                           |
|                                 |                | `L2: {2, 100f, 15f}` | Default Level 2: MaxDist 100f, UpdateRate 15fps                          |

### 3.4. Nested: DebugConfiguration

Debug and visualization configuration.

| Field Name                      | Type           | Default Value        | Description                                                              |
|---------------------------------|----------------|----------------------|--------------------------------------------------------------------------|
| `showAnimationGizmos`           | `bool`         | `false`              | Show animation state gizmos in scene view.                               |
| `showTransitionTimelines`       | `bool`         | `false`              | Show transition timelines.                                               |
| `showStateHistory`              | `bool`         | `false`              | Show state history.                                                      |
| `logStateTransitions`           | `bool`         | `false`              | Log state transitions.                                                   |
| `logPerformanceMetrics`         | `bool`         | `false`              | Log performance metrics.                                                 |
| `validateTransitions`           | `bool`         | `true`               | Validate transitions.                                                    |
| `movementStateColor`            | `Color`        | `Color.blue`         | Color for movement state gizmos.                                         |
| `weaponStateColor`              | `Color`        | `Color.red`          | Color for weapon state gizmos.                                           |
| `transitionColor`               | `Color`        | `Color.yellow`       | Color for transition gizmos.                                             |

### 3.5. Nested: MovementStateMapping

Maps a `MovementSubState` enum to an `AnimationClip` and its properties.

| Field Name                      | Type                | Default Value        | Description                                                              |
|---------------------------------|---------------------|----------------------|--------------------------------------------------------------------------|
| `state`                         | `MovementSubState`  | -                    | Movement state.                                                          |
| `animationClip`                 | `AnimationClip`     | `null`               | Animation clip for this state.                                           |
| `customTransitionDuration`      | `float`             | `-1f`                | Custom transition duration (-1 means use default).                       |
| `speedMultiplier`               | `float`             | `1f`                 | Animation speed multiplier (0.1-3).                                      |
| `isLooping`                     | `bool`              | `true`               | Whether this animation should loop.                                      |

### 3.6. Nested: WeaponStateMapping

Maps `WeaponSubState` and `WeaponSubModuleState` enums to an `AnimationClip` and its properties.

| Field Name                      | Type                   | Default Value        | Description                                                              |
|---------------------------------|------------------------|----------------------|--------------------------------------------------------------------------|
| `weaponState`                   | `WeaponSubState`       | -                    | Weapon state.                                                            |
| `weaponModule`                  | `WeaponSubModuleState` | -                    | Weapon module.                                                           |
| `animationClip`                 | `AnimationClip`        | `null`               | Animation clip for this state/module combination.                        |
| `customTransitionDuration`      | `float`                | `-1f`                | Custom transition duration (-1 means use default).                       |
| `speedMultiplier`               | `float`                | `1f`                 | Animation speed multiplier (0.1-3).                                      |
| `isLooping`                     | `bool`                 | `false`              | Whether this animation should loop.                                      |

### 3.7. Nested: LODLevel

Defines an animation Level of Detail.

| Field Name                      | Type           | Default Value        | Description                                                              |
|---------------------------------|----------------|----------------------|--------------------------------------------------------------------------|
| `level`                         | `int`          | -                    | LOD level (0 = highest quality).                                         |
| `maxDistance`                   | `float`        | -                    | Maximum distance for this LOD level.                                     |
| `updateRate`                    | `float`        | -                    | Update rate (FPS) for this LOD level.                                    |

## 4. HealthSystemConfiguration.cs

Configuration for health system, UI, and death effects. Located at `Assets/Scripts/ScriptableObjects/HealthSystemConfiguration.cs`.

| Field Name                      | Type           | Default Value        | Description                                                              |
|---------------------------------|----------------|----------------------|--------------------------------------------------------------------------|
| **Health Bar UI Settings**      |                |                      |                                                                          |
| `healthBarPrefab`               | `GameObject`   | `null`               | The prefab for health bar UI elements.                                   |
| `healthBarUpdateInterval`       | `float`        | `0.05f`              | Interval (seconds) between health bar UI updates.                        |
| `healthBarPoolSize`             | `int`          | `150`                | The size of the health bar UI pool.                                      |
| `strictHealthBarReuse`          | `bool`         | `true`               | Whether to allow health bars to be reused immediately or enforce cooldown. |
| `healthBarReuseCooldown`        | `float`        | `0.5f`               | Cooldown period (sec) before a health bar can be reused.                 |
| **Detection Settings**          |                |                      |                                                                          |
| `healthBarShowRadius`           | `float`        | `20f`                | The radius within which health bars are shown.                           |
| **Visibility Settings**         |                |                      |                                                                          |
| `healthBarHideDelay`            | `float`        | `0.5f`               | Delay (sec) before hiding health bar after losing detection.             |
| `alwaysShowHealthBars`          | `bool`         | `false`              | Whether health bars should always be visible regardless of detection.    |
| **Position Settings**           |                |                      |                                                                          |
| `defaultHealthBarOffset`        | `Vector3`      | `(0, 2.0f, 0)`       | Default offset for health bar position.                                  |
| **Animation Settings (Death Effects)** |         |                      |                                                                          |
| `deathDissolveStartTime`        | `float`        | `3.0f`               | Time (sec) to wait before starting dissolve effect after death.          |
| `deathDissolveDuration`         | `float`        | `2.0f`               | Duration of the dissolve effect.                                         |
| **Debug Settings**              |                |                      |                                                                          |
| `showDebugVisualizations`       | `bool`         | `true`               | Whether to show debug visualizations.                                    |
| `autoAttachVisualizer`          | `bool`         | `true`               | Auto attach health bar visualizer to player for debugging.               |
