# Aiming Module Documentation

## Overview

The `AimingModule` is responsible for managing the player's aiming system, including target detection, selection, and aiming mechanics. It works closely with the `MovementModule` and `AnimationModule` to provide a cohesive aiming experience.

## Features

- Detects potential targets within a configurable cone/sphere
- Selects the most appropriate target based on distance, angle, and other factors
- Controls upper body rotation for aiming
- Communicates with the `MovementModule` for whole-body rotation when needed
- Provides aim assist functionality for smoother targeting

## Implementation

The `AimingModule` implements the `IAimingModule` interface and several other interfaces for state management and input handling:

```csharp
public class AimingModule : SerializedMonoBehaviour, IAimingModule
    , INeedSubModule<WeaponSubModuleState, SubModule.Aiming.AimingSubModule>
    , INeedInput<CharacterParameter>
{
    [field: SerializeField] public int ControllerIndex { get; private set; }
    [SerializeField] private List<MainState> mainState;
    List<MainState> IModule.MainState => MainState;
    public List<MainState> MainState => mainState;
    [SerializeField] private WeaponSubState subState;
    [field: SerializeField] public WeaponSubModuleState SubModuleState { get; private set; }
    
    // Reference to the configuration scriptable object
    [SerializeField] private AimingConfiguration aimingConfig;
    
    // Aiming methods
    // ...
}
```

## Target Detection

The `AimingModule` uses a combination of cone and sphere detection to identify potential targets:

```csharp
private void UpdateDetectionCache()
{
    if (detectionTarget == null || ecsWorld == null || !ecsWorld.IsCreated)
        return;

    // Cache previous target state before updating
    bool previousHasTarget = m_hasCachedTarget;

    // Get current target from ECS
    var (currentTarget, currentPosition, hasTarget, isInFOV, lastKnownDistance, score) =
        detectionTarget.GetCurrentTarget();
        
    // Update cached target information
    m_hasCachedTarget = hasTarget;
    if (hasTarget)
    {
        m_cachedCurrentTargetPosition = currentPosition;
    }
    
    // Handle target state changes
    if (previousHasTarget && !m_hasCachedTarget)
    {
        // Target lost
        OnTargetLost();
    }
    else if (!previousHasTarget && m_hasCachedTarget)
    {
        // New target acquired
        OnNewTargetAcquired();
    }
}
```

## Aiming States

The `AimingModule` manages several aiming states:

- **Idle**: Not aiming at any target
- **Aiming**: Actively aiming at a target
- **Shooting**: Shooting at the current target

### State Transitions

```csharp
public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
{
    if (!enabled) return;

    SubState = IsAiming ? WeaponSubState.Aiming : WeaponSubState.Idle;

    // Aiming logic here
    switch (SubState)
    {
        case WeaponSubState.Idle:
            StopAiming();
            break;
        case WeaponSubState.Aiming:
            StartAiming();
            break;
        case WeaponSubState.Shooting:
            // Shooting logic
            break;
    }
    
    // Update the aim target position
    UpdateAimTargetPosition();
    
    // Update the AimTarget in CharacterParameters for MovementModule to use
    if (IsAiming && m_hasCachedTarget && m_target.HasValue)
    {
        m_characterParameters.SetAimTarget(m_target.Value);
    }
}
```

## Aiming Mechanics

The module handles aiming mechanics through several components:

### Upper Body Rotation

```csharp
public void StartAiming()
{
    ragdollAiming.enabled = true;
    ragdollAiming.weight = 1;
}

public void StopAiming()
{
    ragdollAiming.enabled = false;
    ragdollAiming.weight = 0;
}
```

### Target Selection

```csharp
private void UpdateAimTargetPosition()
{
    // Check if we have a valid target
    bool isValidTarget = m_hasCachedTarget;
    Vector3 targetPosition = m_hasCachedTarget ? m_cachedCurrentTargetPosition : Vector3.zero;
    
    // Check if target is within field of view
    bool isTargetBehind = false;
    if (isValidTarget)
    {
        Vector3 directionToTarget = (targetPosition - playerTransform.position).normalized;
        float angleToTarget = Vector3.Angle(playerTransform.forward, directionToTarget);
        isTargetBehind = angleToTarget > aimingConfig.coneAngle;
    }
    
    if (isValidTarget)
    {
        // Set the target for aiming
        ragdollAiming.target = m_aimTarget;
        m_lookAtIK.solver.target = m_aimTarget;
        IsAiming = true;
        m_lastTarget = m_hasCachedTarget ? m_cachedCurrentTargetPosition : m_target.Value;

        EventManager.Broadcast(new OnAimingOnTargetEvent(m_lastTarget));

        // If target is behind and we allow non-FOV targets, trigger fast rotation
        if (isTargetBehind && aimingConfig != null && aimingConfig.allowNonFOVTargets)
        {
            EventManager.Broadcast(new OnRequireFastRotationEvent(targetPosition));
        }
    }
    else
    {
        // No valid target to aim at
        ragdollAiming.target = null;
        m_lookAtIK.solver.target = null;
        IsAiming = false;

        EventManager.Broadcast(new OnUnAimingTargetEvent());
    }
}
```

## Configuration

The `AimingModule` is configured through the `AimingConfiguration` ScriptableObject:

```csharp
[CreateAssetMenu(fileName = "AimingConfiguration", menuName = "Configuration/AimingConfiguration")]
public class AimingConfiguration : ScriptableObject
{
    [Header("FOV Parameters")]
    [Tooltip("Angle of the detection cone in degrees")]
    public float coneAngle = 45f;

    [Tooltip("Range of the detection cone")]
    public float coneRange = 10f;

    [Tooltip("Radius of the detection sphere")]
    public float detectionRadius = 6f;

    [Header("Target Selection")]
    [Tooltip("Whether to allow targeting enemies outside the player's field of view")]
    public bool allowNonFOVTargets = false;
    
    [Header("AAA Aim Assist")]
    [Tooltip("Degrees to temporarily expand FOV when a target is near the edge")] 
    public float dynamicFOVExpansion = 15f;
    
    [Tooltip("Angle in degrees for sticky targeting")] 
    public float stickyAngle = 10f;
    
    [Tooltip("Distance in meters for sticky targeting")] 
    public float stickyDistance = 2.5f;
    
    [Tooltip("Degrees per second for auto-rotation toward target")] 
    public float aimAssistSpeed = 120f;

    [Tooltip("How quickly to interpolate to a new target")]
    [Range(0.01f, 1f)]
    public float targetInterpolationSpeed = 0.15f;
}
```

## Integration with ECS

The `AimingModule` integrates with Unity's Entity Component System (ECS) for efficient target detection:

```csharp
private void UpdateECSDetectionParameters()
{
    if (ecsWorld == null || !ecsWorld.IsCreated)
        return;
        
    // Get all entities with SphereDetectSensorComponent
    var query = ecsWorld.EntityManager.CreateEntityQuery(
        ComponentType.ReadWrite<SphereDetectSensorComponent>()
    );
    
    // Update detection parameters
    var entities = query.ToEntityArray(Allocator.Temp);
    foreach (var entity in entities)
    {
        var component = ecsWorld.EntityManager.GetComponentData<SphereDetectSensorComponent>(entity);
        component.DetectionAngle = aimingConfig.coneAngle;
        component.DetectionRange = aimingConfig.coneRange;
        component.DetectionRadius = aimingConfig.detectionRadius;
        ecsWorld.EntityManager.SetComponentData(entity, component);
    }
    
    entities.Dispose();
}
```

## Integration with Other Modules

### Movement Module

The `AimingModule` communicates with the `MovementModule` to trigger character rotation when aiming:

```csharp
// If target is behind and we allow non-FOV targets, trigger fast rotation
if (isTargetBehind && aimingConfig != null && aimingConfig.allowNonFOVTargets)
{
    EventManager.Broadcast(new OnRequireFastRotationEvent(targetPosition));
}
```

### Animation Module

The `AimingModule` communicates with the `AnimationModule` to play appropriate animations during aiming:

```csharp
public void StartAiming()
{
    ragdollAiming.enabled = true;
    ragdollAiming.weight = 1;
    
    // Notify animation module of aiming state
    EventManager.Broadcast(new OnAimingStateChangeEvent(true));
}

public void StopAiming()
{
    ragdollAiming.enabled = false;
    ragdollAiming.weight = 0;
    
    // Notify animation module of aiming state
    EventManager.Broadcast(new OnAimingStateChangeEvent(false));
}
```

## Best Practices

1. **Use Configuration ScriptableObjects**: Keep aiming parameters configurable
2. **Implement Proper Target Selection**: Use clear criteria for target selection
3. **Handle Edge Cases**: Implement special handling for edge cases like target loss
4. **Use Event-Based Communication**: Communicate with other modules through events
5. **Optimize Target Detection**: Use ECS for efficient target detection

## Common Issues and Solutions

### Aiming Is Not Responsive

- Check target detection parameters
- Verify aiming state transitions
- Adjust aim assist parameters

### Character Doesn't Rotate Properly

- Verify communication with the `MovementModule`
- Check rotation event broadcasting
- Adjust rotation speed parameters

### Target Selection Is Incorrect

- Review target selection criteria
- Check field of view calculations
- Verify target priority logic
