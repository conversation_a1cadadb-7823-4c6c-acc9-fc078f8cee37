# GPU ECS Animator Integration Guide

This document provides detailed information about the GPU ECS Animator integration in the project, focusing on optimizing animation performance, especially for mobile devices like the Amazon Fire HD 10.

## Table of Contents

1. [Overview](#overview)
2. [Setup and Configuration](#setup-and-configuration)
3. [Animation System Architecture](#animation-system-architecture)
4. [Performance Optimization](#performance-optimization)
5. [Common Issues and Solutions](#common-issues-and-solutions)
6. [Mobile-Specific Considerations](#mobile-specific-considerations)
7. [Implementation Examples](#implementation-examples)
8. [Troubleshooting](#troubleshooting)

## Overview

The GPU ECS Animator is a high-performance animation system that leverages the GPU for efficient character animation in Unity DOTS. Key benefits include:

- **GPU-Based Animation**: Offloads animation calculations to the GPU
- **ECS Integration**: Seamlessly works with Unity's Entity Component System
- **Instanced Rendering**: Efficiently renders many similar characters
- **Scalability**: Handles hundreds of animated characters with minimal CPU overhead

The project uses GPU ECS Animator for all character animations, including enemies and the player character.

## Setup and Configuration

### Required Components

For each animated character, the following components are required:

1. **GpuEcsAnimatorBehaviour**: Main component that handles GPU animation
2. **GpuEcsAnimatedMeshBehaviour**: Links the mesh to the animation system
3. **CharacterAnimatorReference**: Links the ECS entity to the animator entity
4. **CharacterMovementState**: Stores the current animation state

### Animation Configuration

Animations are defined using animation IDs:

```csharp
public enum EnemyAnimationIDs
{
    Idle = 0,
    Walk = 1,
    Attack = 2,
    Hit = 3,
    Dead = 4
}
```

These IDs must match the baked animation indices in the GPU ECS Animator.

### Baking Process

To prepare animations for GPU ECS Animator:

1. Import character model and animations
2. Set up the animator controller with all required animations
3. Use the GPU ECS Animation Baker to bake animations
4. Configure the GpuEcsAnimatorBehaviour with the baked data

## Animation System Architecture

### Core Components

```csharp
// Character movement state for animation
public struct CharacterMovementState : IComponentData
{
    public float Speed;
    public bool IsMoving;
    public bool IsAttacking;
    public float3 Direction;
    public int CurrentAnimationID;
}

// Character animator reference
public struct CharacterAnimatorReference : IComponentData
{
    public Entity AnimatorEntity;
}

// Animation settings
public struct CharacterSettings : IComponentData
{
    public float WalkSpeed;
    public float RunSpeed;
    public float AnimationBlendSpeed;
}
```

### Key Systems

#### EnemyAnimationSystem

The `EnemyAnimationSystem` is responsible for updating character animations based on movement and state:

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class EnemyAnimationSystem : SystemBase
{
    private ComponentLookup<GpuEcsAnimatorControlComponent> m_AnimatorControlLookup;
    private ComponentLookup<GpuEcsAnimatorControlStateComponent> m_AnimatorStateLookup;
    private ComponentLookup<GpuEcsAnimationDataBufferComponent> m_AnimationDataLookup;
    
    protected override void OnCreate()
    {
        m_AnimatorControlLookup = GetComponentLookup<GpuEcsAnimatorControlComponent>(false);
        m_AnimatorStateLookup = GetComponentLookup<GpuEcsAnimatorControlStateComponent>(false);
        m_AnimationDataLookup = GetComponentLookup<GpuEcsAnimationDataBufferComponent>(true);
        
        RequireForUpdate<CharacterMovementState>();
    }
    
    protected override void OnUpdate()
    {
        // Update component lookups
        m_AnimatorControlLookup.Update(this);
        m_AnimatorStateLookup.Update(this);
        m_AnimationDataLookup.Update(this);
        
        // Handle dead entities separately
        foreach (var (movementState, animatorRef, deadTag, entity) in
                 SystemAPI.Query<RefRW<CharacterMovementState>, RefRO<CharacterAnimatorReference>, RefRO<DeadTag>>().WithEntityAccess())
        {
            if (animatorRef.ValueRO.AnimatorEntity == Entity.Null)
                continue;
                
            // Force dead animation for dead entities
            movementState.ValueRW.CurrentAnimationID = (int)EnemyAnimationIDs.Dead;
            movementState.ValueRW.IsMoving = false;
            movementState.ValueRW.IsAttacking = false;
            
            // Update animator
            UpdateAnimator(animatorRef.ValueRO.AnimatorEntity, (int)EnemyAnimationIDs.Dead);
        }
        
        // Create job for non-dead entities
        var updateAnimationsJob = new UpdateAnimationsJob
        {
            AnimatorControlLookup = m_AnimatorControlLookup,
            AnimatorStateLookup = m_AnimatorStateLookup,
            AnimationDataLookup = m_AnimationDataLookup,
            DeadTagLookup = SystemAPI.GetComponentLookup<DeadTag>(true),
            DeltaTime = SystemAPI.Time.DeltaTime,
            ElapsedTime = (float)SystemAPI.Time.ElapsedTime
        };
        
        // Schedule the job
        Dependency = updateAnimationsJob.Schedule(Dependency);
    }
    
    private void UpdateAnimator(Entity animatorEntity, int animationID)
    {
        if (!m_AnimatorControlLookup.HasComponent(animatorEntity) ||
            !m_AnimatorStateLookup.HasComponent(animatorEntity))
            return;
            
        var control = m_AnimatorControlLookup[animatorEntity];
        var state = m_AnimatorStateLookup[animatorEntity];
        
        // Set the animation ID
        control.AnimationId = animationID;
        
        // Update the components
        m_AnimatorControlLookup[animatorEntity] = control;
        m_AnimatorStateLookup[animatorEntity] = state;
    }
}
```

#### UpdateAnimationsJob

The `UpdateAnimationsJob` handles animation updates for non-dead entities:

```csharp
[BurstCompile]
private partial struct UpdateAnimationsJob : IJobEntity
{
    [NativeDisableParallelForRestriction]
    public ComponentLookup<GpuEcsAnimatorControlComponent> AnimatorControlLookup;
    
    [NativeDisableParallelForRestriction]
    public ComponentLookup<GpuEcsAnimatorControlStateComponent> AnimatorStateLookup;
    
    [ReadOnly]
    public ComponentLookup<GpuEcsAnimationDataBufferComponent> AnimationDataLookup;
    
    [ReadOnly]
    public ComponentLookup<DeadTag> DeadTagLookup;
    
    public float DeltaTime;
    public float ElapsedTime;
    
    void Execute(Entity entity,
                ref CharacterMovementState movementState,
                in CharacterAnimatorReference animatorRef,
                in CharacterSettings settings)
    {
        // Skip if entity is dead or has no animator
        if (DeadTagLookup.HasComponent(entity) || animatorRef.AnimatorEntity == Entity.Null)
            return;
            
        // Determine animation based on movement state
        int targetAnimationID;
        
        if (movementState.IsAttacking)
        {
            targetAnimationID = (int)EnemyAnimationIDs.Attack;
        }
        else if (movementState.IsMoving)
        {
            // Choose between walk and run based on speed
            float speed = math.length(movementState.Direction);
            targetAnimationID = speed > settings.WalkSpeed ? (int)EnemyAnimationIDs.Run : (int)EnemyAnimationIDs.Walk;
        }
        else
        {
            targetAnimationID = (int)EnemyAnimationIDs.Idle;
        }
        
        // Update movement state
        movementState.CurrentAnimationID = targetAnimationID;
        
        // Update animator
        UpdateAnimator(animatorRef.AnimatorEntity, targetAnimationID);
    }
    
    private void UpdateAnimator(Entity animatorEntity, int animationID)
    {
        if (!AnimatorControlLookup.HasComponent(animatorEntity) ||
            !AnimatorStateLookup.HasComponent(animatorEntity))
            return;
            
        var control = AnimatorControlLookup[animatorEntity];
        var state = AnimatorStateLookup[animatorEntity];
        
        // Set the animation ID
        control.AnimationId = animationID;
        
        // Update the components
        AnimatorControlLookup[animatorEntity] = control;
        AnimatorStateLookup[animatorEntity] = state;
    }
}
```

#### DeathAnimationSystem

The `DeathAnimationSystem` handles death animations:

```csharp
[UpdateAfter(typeof(DamageSystem))]
[UpdateBefore(typeof(EnemyAnimationSystem))]
public partial class DeathAnimationSystem : SystemBase
{
    private EntityCommandBufferSystem m_EndSimulationEcbSystem;
    
    protected override void OnCreate()
    {
        m_EndSimulationEcbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
        RequireForUpdate<DeadTag>();
    }
    
    protected override void OnUpdate()
    {
        var ecb = m_EndSimulationEcbSystem.CreateCommandBuffer();
        
        // Handle newly dead entities
        Entities
            .WithNone<DeathAnimationStartedTag>()
            .WithAll<DeadTag>()
            .ForEach((Entity entity,
                     ref CharacterMovementState movementState,
                     in CharacterAnimatorReference animatorRef) =>
            {
                // Skip if animator entity is invalid
                if (animatorRef.AnimatorEntity == Entity.Null)
                    return;
                    
                // Update movement state for death animation
                movementState.CurrentAnimationID = (int)EnemyAnimationIDs.Dead;
                movementState.IsMoving = false;
                movementState.IsAttacking = false;
                
                // Add tag to mark that death animation has started
                ecb.AddComponent<DeathAnimationStartedTag>(entity);
                
                // Remove navigation components
                if (HasComponent<AgentBody>(entity))
                {
                    ecb.RemoveComponent<AgentBody>(entity);
                }
                
                if (HasComponent<SetDestination>(entity))
                {
                    ecb.RemoveComponent<SetDestination>(entity);
                }
            }).WithoutBurst().Run();
            
        m_EndSimulationEcbSystem.AddJobHandleForProducer(Dependency);
    }
}
```

## Performance Optimization

### Animation LOD System

Implement an Animation LOD system to reduce animation processing for distant entities:

```csharp
// Animation LOD component
public struct AnimationLODComponent : IComponentData
{
    public int SampleRate;        // How often to update animation (1 = every frame, 2 = every other frame, etc.)
    public float BlendSpeed;      // How quickly to blend between animations
    public bool EnableRootMotion; // Whether to use root motion
}

// Animation LOD system
[UpdateBefore(typeof(EnemyAnimationSystem))]
public partial class AnimationLODSystem : SystemBase
{
    protected override void OnUpdate()
    {
        float3 playerPosition = float3.zero;
        
        // Get player position
        foreach (var playerTransform in SystemAPI.Query<RefRO<PlayerTransform>>().WithAll<PlayerTag>())
        {
            playerPosition = playerTransform.ValueRO.Position;
            break;
        }
        
        // Update animation LOD based on distance
        float fullAnimationRadius = 20f;
        float reducedAnimationRadius = 40f;
        
        Entities
            .WithAll<CharacterAnimatorReference>()
            .ForEach((Entity entity, ref AnimationLODComponent animLOD, in LocalTransform transform) =>
            {
                float distanceSq = math.distancesq(transform.Position, playerPosition);
                
                if (distanceSq <= fullAnimationRadius * fullAnimationRadius)
                {
                    // Full animation for close entities
                    animLOD.SampleRate = 1;
                    animLOD.BlendSpeed = 0.2f;
                    animLOD.EnableRootMotion = true;
                }
                else if (distanceSq <= reducedAnimationRadius * reducedAnimationRadius)
                {
                    // Reduced animation for medium-distance entities
                    animLOD.SampleRate = 2;
                    animLOD.BlendSpeed = 0.1f;
                    animLOD.EnableRootMotion = false;
                }
                else
                {
                    // Minimal animation for distant entities
                    animLOD.SampleRate = 4;
                    animLOD.BlendSpeed = 0.05f;
                    animLOD.EnableRootMotion = false;
                }
            }).ScheduleParallel();
    }
}
```

### Animation Culling

Implement animation culling to disable animations for off-screen entities:

```csharp
// Animation culling component
public struct AnimationCullingComponent : IComponentData
{
    public bool IsVisible;
    public float LastVisibleTime;
    public float CullingDelay; // How long to keep animating after becoming invisible
}

// Animation culling system
[UpdateBefore(typeof(EnemyAnimationSystem))]
public partial class AnimationCullingSystem : SystemBase
{
    protected override void OnUpdate()
    {
        // Get main camera
        Camera mainCamera = Camera.main;
        if (mainCamera == null) return;
        
        // Create frustum planes
        Plane[] frustumPlanes = GeometryUtility.CalculateFrustumPlanes(mainCamera);
        
        // Current time
        float currentTime = (float)SystemAPI.Time.ElapsedTime;
        
        // Update culling state
        Entities
            .WithAll<CharacterAnimatorReference>()
            .ForEach((Entity entity, ref AnimationCullingComponent culling, in LocalTransform transform) =>
            {
                // Create bounds for the entity
                Bounds bounds = new Bounds(transform.Position, new Vector3(2f, 2f, 2f));
                
                // Check if visible
                bool isVisible = GeometryUtility.TestPlanesAABB(frustumPlanes, bounds);
                
                // Update culling component
                if (isVisible)
                {
                    culling.IsVisible = true;
                    culling.LastVisibleTime = currentTime;
                }
                else
                {
                    // Only set to invisible if it's been invisible for longer than the culling delay
                    if (currentTime - culling.LastVisibleTime > culling.CullingDelay)
                    {
                        culling.IsVisible = false;
                    }
                }
            }).ScheduleParallel();
    }
}
```

### Animation Rate Control

Implement animation rate control to adjust animation update frequency:

```csharp
// Animation rate control component
public struct AnimationRateControlComponent : IComponentData
{
    public int UpdateInterval;  // How many frames between updates
    public int FrameCounter;    // Current frame counter
    public bool ShouldUpdate;   // Whether to update this frame
}

// Animation rate control system
[UpdateBefore(typeof(EnemyAnimationSystem))]
public partial class AnimationRateControlSystem : SystemBase
{
    private int frameCount = 0;
    
    protected override void OnUpdate()
    {
        frameCount++;
        
        // Update rate control
        Entities
            .WithAll<CharacterAnimatorReference>()
            .ForEach((Entity entity, ref AnimationRateControlComponent rateControl, in AnimationLODComponent lod) =>
            {
                // Set update interval based on LOD
                rateControl.UpdateInterval = lod.SampleRate;
                
                // Increment frame counter
                rateControl.FrameCounter++;
                
                // Check if should update this frame
                if (rateControl.FrameCounter >= rateControl.UpdateInterval)
                {
                    rateControl.ShouldUpdate = true;
                    rateControl.FrameCounter = 0;
                }
                else
                {
                    rateControl.ShouldUpdate = false;
                }
            }).ScheduleParallel();
    }
}
```

### Modified EnemyAnimationSystem

Update the `EnemyAnimationSystem` to respect LOD and culling:

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(AnimationRateControlSystem))]
public partial class EnemyAnimationSystem : SystemBase
{
    // ... existing code ...
    
    protected override void OnUpdate()
    {
        // ... existing code ...
        
        // Create job for non-dead entities with LOD and culling
        var updateAnimationsJob = new UpdateAnimationsJob
        {
            AnimatorControlLookup = m_AnimatorControlLookup,
            AnimatorStateLookup = m_AnimatorStateLookup,
            AnimationDataLookup = m_AnimationDataLookup,
            DeadTagLookup = SystemAPI.GetComponentLookup<DeadTag>(true),
            AnimationCullingLookup = SystemAPI.GetComponentLookup<AnimationCullingComponent>(true),
            AnimationRateControlLookup = SystemAPI.GetComponentLookup<AnimationRateControlComponent>(true),
            DeltaTime = SystemAPI.Time.DeltaTime,
            ElapsedTime = (float)SystemAPI.Time.ElapsedTime
        };
        
        // Schedule the job
        Dependency = updateAnimationsJob.Schedule(Dependency);
    }
}

[BurstCompile]
private partial struct UpdateAnimationsJob : IJobEntity
{
    // ... existing fields ...
    
    [ReadOnly]
    public ComponentLookup<AnimationCullingComponent> AnimationCullingLookup;
    
    [ReadOnly]
    public ComponentLookup<AnimationRateControlComponent> AnimationRateControlLookup;
    
    void Execute(Entity entity,
                ref CharacterMovementState movementState,
                in CharacterAnimatorReference animatorRef,
                in CharacterSettings settings)
    {
        // Skip if entity is dead or has no animator
        if (DeadTagLookup.HasComponent(entity) || animatorRef.AnimatorEntity == Entity.Null)
            return;
            
        // Skip if entity is culled
        if (AnimationCullingLookup.HasComponent(entity) && !AnimationCullingLookup[entity].IsVisible)
            return;
            
        // Skip if entity should not update this frame
        if (AnimationRateControlLookup.HasComponent(entity) && !AnimationRateControlLookup[entity].ShouldUpdate)
            return;
            
        // ... existing animation logic ...
    }
}
```

## Common Issues and Solutions

### Issue: High CPU Usage with Many Animated Characters

**Symptoms:**
- FPS drops when many characters are visible
- CPU usage spikes during animation updates
- Performance degrades on mobile devices

**Solutions:**
1. Implement Animation LOD system
2. Use animation culling for off-screen entities
3. Reduce animation sample rate for distant entities
4. Simplify animations for mobile devices

### Issue: Animation Glitches During Transitions

**Symptoms:**
- Characters "pop" between animations
- Animations blend incorrectly
- Visual artifacts during transitions

**Solutions:**
1. Adjust blend times based on animation type
2. Ensure animations have proper looping settings
3. Check for animation clip compatibility
4. Verify animation IDs match baked animations

### Issue: Memory Usage Growth

**Symptoms:**
- Memory usage increases over time
- Performance degrades after extended play
- Out of memory errors on mobile devices

**Solutions:**
1. Optimize animation data size
2. Reduce animation clip resolution
3. Share animation data between similar characters
4. Implement proper cleanup for destroyed entities

### Issue: GPU Performance Bottlenecks

**Symptoms:**
- GPU usage spikes with many animated characters
- Rendering time dominates frame time
- Performance issues on low-end GPUs

**Solutions:**
1. Reduce animation complexity for mobile
2. Implement mesh LOD for characters
3. Use simpler shaders for animated characters
4. Reduce bone count for distant characters

## Mobile-Specific Considerations

### Optimizing for Mobile GPUs

1. **Reduce Animation Complexity**:
   - Use fewer bones for mobile animations
   - Simplify animation curves
   - Reduce animation sample rate

2. **Adjust Animation Quality**:
   - Implement device-specific animation settings
   - Use lower quality animations on low-end devices
   - Reduce animation blend times on mobile

3. **Manage Memory Usage**:
   - Optimize animation data size
   - Share animation data between characters
   - Unload unused animations

### Device-Specific Settings

Implement device-specific animation settings:

```csharp
public class AnimationQualityManager : MonoBehaviour
{
    [Serializable]
    public class DeviceSettings
    {
        public string deviceName;
        public int maxAnimatedEntities;
        public int animationSampleRate;
        public float animationCullingDistance;
        public bool useRootMotion;
    }
    
    public List<DeviceSettings> deviceSettings = new List<DeviceSettings>();
    public DeviceSettings defaultSettings;
    
    private DeviceSettings currentSettings;
    
    private void Awake()
    {
        // Detect device and apply settings
        string deviceModel = SystemInfo.deviceModel;
        
        // Find matching device settings
        currentSettings = deviceSettings.Find(s => deviceModel.Contains(s.deviceName));
        
        // Use default if no match found
        if (currentSettings == null)
        {
            currentSettings = defaultSettings;
        }
        
        // Apply settings
        ApplySettings();
    }
    
    private void ApplySettings()
    {
        // Get animation systems
        var world = World.DefaultGameObjectInjectionWorld;
        var animLODSystem = world.GetExistingSystemManaged<AnimationLODSystem>();
        var animCullingSystem = world.GetExistingSystemManaged<AnimationCullingSystem>();
        var spawnerSystem = world.GetExistingSystemManaged<CharacterSpawnerSystem>();
        
        // Apply settings
        if (animLODSystem != null)
        {
            animLODSystem.SetDefaultSampleRate(currentSettings.animationSampleRate);
            animLODSystem.SetUseRootMotion(currentSettings.useRootMotion);
        }
        
        if (animCullingSystem != null)
        {
            animCullingSystem.SetCullingDistance(currentSettings.animationCullingDistance);
        }
        
        if (spawnerSystem != null)
        {
            spawnerSystem.SetMaxEntityCount(currentSettings.maxAnimatedEntities);
        }
    }
}
```

### Amazon Fire HD 10 Specific Optimizations

For the Amazon Fire HD 10 (9th generation) specifically:

1. **Reduce Entity Count**:
   - Limit to 50-75 animated entities
   - Implement aggressive culling

2. **Optimize Animation Settings**:
   - Use animation sample rate of 2-3 frames
   - Disable root motion for distant entities
   - Reduce animation blend times

3. **Simplify Animations**:
   - Use simplified animation sets
   - Reduce bone count for distant entities
   - Disable IK for all entities

## Implementation Examples

### Adding GPU ECS Animator to a Character

```csharp
// In your character authoring component
public class CharacterAuthoring : MonoBehaviour, IConvertGameObjectToEntity
{
    public GameObject characterModel;
    public GpuEcsAnimatorBehaviour animatorBehaviour;
    
    public void Convert(Entity entity, EntityManager dstManager, GameObjectConversionSystem conversionSystem)
    {
        // Convert the animator
        Entity animatorEntity = conversionSystem.GetPrimaryEntity(animatorBehaviour);
        
        // Add animator reference component
        dstManager.AddComponentData(entity, new CharacterAnimatorReference
        {
            AnimatorEntity = animatorEntity
        });
        
        // Add movement state component
        dstManager.AddComponentData(entity, new CharacterMovementState
        {
            CurrentAnimationID = (int)EnemyAnimationIDs.Idle,
            IsMoving = false,
            IsAttacking = false
        });
        
        // Add animation LOD component
        dstManager.AddComponentData(entity, new AnimationLODComponent
        {
            SampleRate = 1,
            BlendSpeed = 0.2f,
            EnableRootMotion = true
        });
        
        // Add animation culling component
        dstManager.AddComponentData(entity, new AnimationCullingComponent
        {
            IsVisible = true,
            LastVisibleTime = 0f,
            CullingDelay = 0.5f
        });
        
        // Add animation rate control component
        dstManager.AddComponentData(entity, new AnimationRateControlComponent
        {
            UpdateInterval = 1,
            FrameCounter = 0,
            ShouldUpdate = true
        });
    }
}
```

### Triggering Animations from Other Systems

```csharp
// In your AI system
[BurstCompile]
private partial struct AIDecisionJob : IJobEntity
{
    public EntityCommandBuffer.ParallelWriter ECB;
    public float DeltaTime;
    public float AttackRange;
    public float3 PlayerPosition;
    
    void Execute(Entity entity, [EntityIndexInQuery] int sortKey,
                ref CharacterMovementState movementState,
                in LocalTransform transform)
    {
        float3 toPlayer = PlayerPosition - transform.Position;
        float distanceSq = math.lengthsq(toPlayer);
        
        if (distanceSq <= AttackRange * AttackRange)
        {
            // In attack range, trigger attack animation
            movementState.IsAttacking = true;
            movementState.IsMoving = false;
        }
        else
        {
            // Out of range, move towards player
            movementState.IsAttacking = false;
            movementState.IsMoving = true;
            movementState.Direction = math.normalize(toPlayer);
        }
    }
}
```

## Troubleshooting

### Animation Not Playing

**Check List:**
1. Verify animation ID matches baked animation index
2. Ensure animator entity reference is valid
3. Check that entity has required components
4. Verify animation is properly baked
5. Check for animation culling or LOD issues

### Performance Issues

**Check List:**
1. Monitor entity count and reduce if necessary
2. Implement animation LOD and culling
3. Adjust animation sample rate for device
4. Check for GPU bottlenecks
5. Optimize character meshes and textures

### Memory Issues

**Check List:**
1. Check for animation data leaks
2. Verify proper cleanup of destroyed entities
3. Monitor memory usage over time
4. Optimize animation data size
5. Share animation data between similar characters

### Animation Glitches

**Check List:**
1. Check animation transitions and blend times
2. Verify animation clip compatibility
3. Check for animation curve issues
4. Ensure proper animation looping
5. Verify root motion settings
