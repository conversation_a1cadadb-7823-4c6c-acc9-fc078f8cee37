# Configuration Centralization Implementation Plan

## Overview
This plan addresses the critical issue where AimingConfiguration changes don't affect gameplay due to duplicate fields in AimModuleAuthoring and hardcoded values throughout the codebase.

## 🎯 GOALS
1. Make AimingConfiguration the single source of truth for all aiming parameters
2. Update MovementModule to read from MovementModuleConfiguration  
3. Remove all hardcoded values and duplicate fields
4. Follow established ScriptableObject configuration patterns

## 📋 IMPLEMENTATION STEPS

### Step 1: Update AimModuleAuthoring to Use AimingConfiguration

**Files to Modify:**
- `Assets/Scripts/_ECS/Authoring/AimModuleAuthoring.cs`

**Changes:**
1. Add AimingConfiguration reference
2. Remove duplicate fields:
   - `detectionRadius`, `DetectionRange`, `DetectionAngle`
   - `TargetSwitchDistanceThreshold`, `MinTargetSwitchDistance`
   - `TargetSwitchAngleThreshold`, `TargetSwitchScoreThreshold`
3. Add `UpdateValuesFromConfiguration()` method
4. Update <PERSON> to use configuration values

**Code Pattern:**
```csharp
[Header("Configuration")]
[SerializeField] private AimingConfiguration aimingConfig;

private void UpdateValuesFromConfiguration()
{
    if (aimingConfig == null)
    {
        aimingConfig = Resources.Load<AimingConfiguration>("AimingConfiguration");
    }
    
    if (aimingConfig != null)
    {
        detectionRadius = aimingConfig.detectionRadius;
        DetectionRange = aimingConfig.coneRange;
        DetectionAngle = aimingConfig.coneAngleBeforeAiming;
        // ... other fields
    }
}
```

### Step 2: Fix OptimizedDetectionSystem Hardcoded Values

**Files to Modify:**
- `Assets/Scripts/_ECS/Systems/AimDetection/OptimizedDetectionSystem.cs`

**Changes:**
1. Add AimingConfiguration reference to system
2. Replace hardcoded 0.7f/0.3f with distanceWeight/angleWeight
3. Update CalculateScore method
4. Fix EnemyDetectionData.CompareTo method

**Critical Lines to Fix:**
- Line 564: `return (distanceScore * 0.7f) + (angleScore * 0.3f) + proximityBonus;`
- Line 775: `var scoreA = Distance * 0.7f + Angle * 0.3f;`
- Line 776: `var scoreB = other.Distance * 0.7f + other.Angle * 0.3f;`

### Step 3: Update MovementModule Configuration Integration

**Files to Modify:**
- `Assets/Scripts/Module/MovementModule.cs`

**Changes:**
1. Add MovementModuleConfiguration reference
2. Replace hardcoded values with configuration reads
3. Add configuration loading in Awake/Start
4. Update all movement parameters to use config

**Fields to Replace:**
```csharp
// Replace these hardcoded fields:
[SerializeField] private float moveSpeed = 5.0f;
[SerializeField] private float rotationSpeed = 720.0f;
[SerializeField] private float m_targetRotationSpeed = 5f;
[SerializeField] private float m_minRotationAngle = 30f;
// ... with configuration reads
```

### Step 4: Implement Missing AimingConfiguration Features

**Features to Implement:**
1. **coneAngleAfterAiming**: Switch cone angle when aiming starts/stops
2. **targetSwitchHysteresis**: Prevent rapid target switching
3. **targetInterpolationSpeed**: Smooth target transitions
4. **CooldownAfterLostAllEnenies**: Cooldown after losing all targets

### Step 5: Create Configuration Validation System

**New File:**
- `Assets/Scripts/Configuration/ConfigurationValidator.cs`

**Purpose:**
- Validate configuration values at runtime
- Log warnings for invalid configurations
- Provide fallback values
- Debug configuration loading issues

## 🔧 DETAILED IMPLEMENTATION

### AimModuleAuthoring Update

```csharp
public class AimModuleAuthoring : SerializedMonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private AimingConfiguration aimingConfig;
    
    [Header("Detection Settings")]
    public bool EnableKDTree;
    public float cooldownTime = 1.0f;
    public bool canDetect = true;
    public bool canLose = true;
    
    // Remove these duplicate fields:
    // public float detectionRadius = 6f;
    // public float DetectionRange = 20f;
    // public float DetectionAngle = 45f;
    // public float TargetSwitchDistanceThreshold = 0.9f;
    // ... etc
    
    private void Awake()
    {
        UpdateValuesFromConfiguration();
    }
    
    private void UpdateValuesFromConfiguration()
    {
        if (aimingConfig == null)
        {
            aimingConfig = Resources.Load<AimingConfiguration>("AimingConfiguration");
        }
    }
    
    // Add public properties to access config values
    public float DetectionRadius => aimingConfig?.detectionRadius ?? 6f;
    public float DetectionRange => aimingConfig?.coneRange ?? 20f;
    public float DetectionAngle => aimingConfig?.coneAngleBeforeAiming ?? 45f;
    // ... etc
}
```

### MovementModule Configuration Integration

```csharp
public class MovementModule : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private MovementModuleConfiguration movementConfig;
    
    // Remove hardcoded fields, use config instead:
    // [SerializeField] private float moveSpeed = 5.0f;
    // [SerializeField] private float rotationSpeed = 720.0f;
    
    private void Awake()
    {
        LoadConfiguration();
    }
    
    private void LoadConfiguration()
    {
        if (movementConfig == null)
        {
            movementConfig = Resources.Load<MovementModuleConfiguration>("MovementConfiguration");
        }
    }
    
    // Use config values throughout the class:
    private void ApplyMovement(float horizontal, float vertical)
    {
        float currentMoveSpeed = movementConfig?.moveSpeed ?? 5.0f;
        // ... use currentMoveSpeed instead of hardcoded value
    }
}
```

## 🧪 TESTING PLAN

### Test 1: Configuration Changes Take Effect
1. Change `distanceWeight` from 0.9 to 0.1 in AimingConfiguration
2. Verify closest enemy is no longer prioritized
3. Change back and verify closest enemy is prioritized again

### Test 2: Movement Configuration Works
1. Change `moveSpeed` in MovementModuleConfiguration
2. Verify player movement speed changes in game
3. Test other movement parameters

### Test 3: No Hardcoded Overrides
1. Search codebase for hardcoded 0.7f, 0.3f values
2. Verify all are replaced with configuration reads
3. Test that configuration changes affect these calculations

## 📊 SUCCESS METRICS

**Before Fix:**
- Configuration changes: 40% effective
- Hardcoded values: 6+ locations
- Duplicate fields: 8+ fields

**After Fix:**
- Configuration changes: 100% effective  
- Hardcoded values: 0 locations
- Duplicate fields: 0 fields
- Single source of truth: ✅

## 🚀 ROLLOUT STRATEGY

1. **Phase 1**: Fix AimModuleAuthoring (highest impact)
2. **Phase 2**: Fix OptimizedDetectionSystem hardcoded values
3. **Phase 3**: Update MovementModule configuration
4. **Phase 4**: Implement missing features
5. **Phase 5**: Add validation and testing

Each phase can be implemented and tested independently.
