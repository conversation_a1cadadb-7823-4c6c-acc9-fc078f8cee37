# ECS Technical Documentation - Part 4: Health and Damage

## Introduction

This document details the health and damage systems used in the project. These systems are responsible for managing entity health, applying damage, handling death, and displaying health bars.

## Table of Contents

1. [Health Components](#health-components)
2. [Damage Systems](#damage-systems)
3. [Health Bar Systems](#health-bar-systems)
4. [Death Systems](#death-systems)
5. [Implementation Guidelines](#implementation-guidelines)

## Health Components

### Core Health Components

```csharp
// Health component
public struct HealthComponent : IComponentData
{
    public float MaxHealth;
    public float CurrentHealth;
}

// Health bar tag component
public struct HealthBarTagComponent : IComponentData
{
    public Entity UIEntity;
}

// Health bar UI reference
public struct HealthBarUIReference : IComponentData
{
    public Entity UIEntity;
}

// Damage event component
public struct DamageEvent : IComponentData
{
    public Entity Target;
    public float Amount;
    public Entity Source;
    public float3 HitPoint;
}

// Hit tag component
public struct HitTag : IComponentData { }

// Dead tag component
public struct DeadTag : IComponentData { }

// Death timer component
public struct DeathTimerComponent : IComponentData
{
    public float DeathTime;
    public float DissolveStartTime;
    public float DissolveDuration;
}

// Dissolve effect component
public struct DissolveEffectComponent : IComponentData
{
    public float DissolveAmount;
    public bool IsDissolving;
}
```

## Damage Systems

### DamageSystem

The `DamageSystem` applies damage to entities with health components and handles death when health reaches zero.

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateBefore(typeof(HealthBarSpawnSystem))]
public partial class DamageSystem : SystemBase
{
    private EntityCommandBuffer.ParallelWriter ecb;
    private DamageECBSystem commandBufferSystem;

    protected override void OnCreate()
    {
        commandBufferSystem = World.GetOrCreateSystemManaged<DamageECBSystem>();
    }

    protected override void OnUpdate()
    {
        ecb = commandBufferSystem.CreateCommandBuffer().AsParallelWriter();
        float currentTime = (float)Time.ElapsedTime;
        
        // Process damage events
        Entities
            .WithNone<DeadTag>()
            .ForEach((Entity entity, int entityInQueryIndex,
                     ref HealthComponent health,
                     in DamageEvent damageEvent) =>
            {
                // Apply damage
                health.CurrentHealth -= damageEvent.Amount;
                
                // Add hit tag for hit animation
                ecb.AddComponent<HitTag>(entityInQueryIndex, entity);
                
                // Check if entity is dead
                if (health.CurrentHealth <= 0)
                {
                    health.CurrentHealth = 0;
                    
                    // Add dead tag
                    ecb.AddComponent<DeadTag>(entityInQueryIndex, entity);
                    
                    // Add death timer component
                    ecb.AddComponent(entityInQueryIndex, entity, new DeathTimerComponent
                    {
                        DeathTime = currentTime,
                        DissolveStartTime = currentTime + 2.0f, // Start dissolve after 2 seconds
                        DissolveDuration = 1.5f // Dissolve over 1.5 seconds
                    });
                    
                    // Add dissolve effect component
                    ecb.AddComponent(entityInQueryIndex, entity, new DissolveEffectComponent
                    {
                        DissolveAmount = 0f,
                        IsDissolving = false
                    });
                    
                    // Remove any existing tags that should not apply to dead entities
                    if (EntityManager.HasComponent<CurrentTargetTag>(entity))
                    {
                        ecb.RemoveComponent<CurrentTargetTag>(entityInQueryIndex, entity);
                    }
                }
                
                // Remove the damage event
                ecb.RemoveComponent<DamageEvent>(entityInQueryIndex, entity);
            })
            .ScheduleParallel();
            
        commandBufferSystem.AddJobHandleForProducer(Dependency);
    }
}
```

## Health Bar Systems

### HealthBarSpawnSystem

The `HealthBarSpawnSystem` creates and updates health bars for entities with health components.

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class HealthBarSpawnSystem : SystemBase
{
    private EntityQuery healthBarQuery;
    private EntityCommandBufferSystem commandBufferSystem;
    private Entity healthBarPrefab;
    private HealthSystemConfiguration config;

    protected override void OnCreate()
    {
        commandBufferSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
        healthBarQuery = GetEntityQuery(ComponentType.ReadOnly<HealthBarTagComponent>());
        
        // Load health bar prefab
        healthBarPrefab = PrefabManager.Instance.GetPrefab("HealthBarPrefab");
        
        // Load configuration
        config = Resources.Load<HealthSystemConfiguration>("HealthSystemConfiguration");
    }

    protected override void OnUpdate()
    {
        var ecb = commandBufferSystem.CreateCommandBuffer();
        float currentTime = (float)Time.ElapsedTime;
        
        // Process entities with health but no health bar
        Entities
            .WithNone<HealthBarTagComponent, DeadTag>()
            .WithAll<DetectedTag>() // Only detected entities should have health bars
            .ForEach((Entity entity, in HealthComponent health, in LocalToWorld transform) =>
            {
                // Check if entity was recently spawned
                if (EntityManager.HasComponent<SpawnComponent>(entity))
                {
                    var spawnComponent = EntityManager.GetComponentData<SpawnComponent>(entity);
                    float timeSinceSpawn = currentTime - spawnComponent.SpawnTime;
                    
                    // Skip health bar creation for newly spawned entities
                    if (timeSinceSpawn < 0.5f)
                        return;
                }
                
                // Create health bar entity
                var healthBarEntity = ecb.Instantiate(healthBarPrefab);
                
                // Set health bar position above the entity
                var healthBarPosition = transform.Position + new float3(0, 2, 0);
                ecb.SetComponent(healthBarEntity, new Translation { Value = healthBarPosition });
                
                // Link health bar to entity
                ecb.AddComponent(entity, new HealthBarTagComponent { UIEntity = healthBarEntity });
                ecb.AddComponent(healthBarEntity, new HealthBarUIReference { UIEntity = entity });
                
                // Set initial health percentage
                var healthPercentage = health.CurrentHealth / health.MaxHealth;
                ecb.AddComponent(healthBarEntity, new HealthBarPercentage { Value = healthPercentage });
            })
            .WithoutBurst() // Can't use Burst with Resources.Load
            .Run();
            
        // Update existing health bars
        Entities
            .WithAll<HealthBarTagComponent>()
            .ForEach((Entity entity, in HealthComponent health, in LocalToWorld transform, in HealthBarTagComponent healthBarTag) =>
            {
                if (healthBarTag.UIEntity == Entity.Null)
                    return;
                    
                // Update health bar position
                var healthBarPosition = transform.Position + new float3(0, 2, 0);
                ecb.SetComponent(healthBarTag.UIEntity, new Translation { Value = healthBarPosition });
                
                // Update health percentage
                var healthPercentage = health.CurrentHealth / health.MaxHealth;
                ecb.SetComponent(healthBarTag.UIEntity, new HealthBarPercentage { Value = healthPercentage });
                
                // Hide health bar if entity is dead
                if (EntityManager.HasComponent<DeadTag>(entity))
                {
                    ecb.AddComponent<DisableRendering>(healthBarTag.UIEntity);
                }
            })
            .WithoutBurst() // Can't use Burst with EntityManager.HasComponent
            .Run();
            
        // Remove health bars from undetected entities
        Entities
            .WithAll<HealthBarTagComponent>()
            .WithNone<DetectedTag, DeadTag>()
            .ForEach((Entity entity, in HealthBarTagComponent healthBarTag) =>
            {
                if (healthBarTag.UIEntity != Entity.Null)
                {
                    ecb.DestroyEntity(healthBarTag.UIEntity);
                }
                
                ecb.RemoveComponent<HealthBarTagComponent>(entity);
            })
            .WithoutBurst() // Can't use Burst with EntityManager.HasComponent
            .Run();
    }
}
```

## Death Systems

### DeathAnimationSystem

The `DeathAnimationSystem` handles death animations for entities with the `DeadTag`.

```csharp
[UpdateAfter(typeof(DamageSystem))]
[UpdateBefore(typeof(EnemyAnimationSystem))]
public partial class DeathAnimationSystem : SystemBase
{
    private EntityCommandBufferSystem m_EndSimulationEcbSystem;

    protected override void OnCreate()
    {
        m_EndSimulationEcbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
        RequireForUpdate<DeadTag>();
    }

    protected override void OnUpdate()
    {
        var ecb = m_EndSimulationEcbSystem.CreateCommandBuffer();
        
        // Handle newly dead entities - start death animation
        Entities
            .WithNone<DeathAnimationStartedTag>()
            .WithAll<DeadTag>()
            .ForEach((Entity entity,
                     ref CharacterMovementState movementState,
                     in CharacterAnimatorReference animatorRef) =>
            {
                // Skip if animator entity is invalid
                if (animatorRef.AnimatorEntity == Entity.Null)
                    return;
                    
                // Set animation to death
                movementState.CurrentAnimationID = (int)EnemyAnimationIDs.Dead;
                movementState.IsMoving = false;
                movementState.IsAttacking = false;
                
                // Mark death animation as started
                ecb.AddComponent<DeathAnimationStartedTag>(entity);
                
                // Disable collider
                if (EntityManager.HasComponent<PhysicsCollider>(entity))
                {
                    ecb.RemoveComponent<PhysicsCollider>(entity);
                }
                
                // Log for debugging
                DebugLogManager.Instance.Log($"Started death animation for entity {entity.Index}", DebugLogManager.LogType.EnemyAnimation);
            })
            .WithoutBurst() // Can't use Burst with DebugLogManager
            .Run();
    }
}
```

### DeathDissolveSystem

The `DeathDissolveSystem` handles the dissolve effect and entity destruction after death.

```csharp
[UpdateAfter(typeof(DeathAnimationSystem))]
public partial class DeathDissolveSystem : SystemBase
{
    private EntityCommandBufferSystem m_EndSimulationEcbSystem;

    protected override void OnCreate()
    {
        m_EndSimulationEcbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
        RequireForUpdate<DeadTag>();
    }

    protected override void OnUpdate()
    {
        var ecb = m_EndSimulationEcbSystem.CreateCommandBuffer();
        float currentTime = (float)Time.ElapsedTime;
        
        // Handle dissolve effect for dead entities
        Entities
            .WithAll<DeadTag, DeathAnimationStartedTag>()
            .ForEach((Entity entity,
                     ref DissolveEffectComponent dissolveEffect,
                     in DeathTimerComponent deathTimer) =>
            {
                // Check if it's time to start dissolving
                if (!dissolveEffect.IsDissolving && currentTime >= deathTimer.DissolveStartTime)
                {
                    dissolveEffect.IsDissolving = true;
                    
                    // Log for debugging
                    DebugLogManager.Instance.Log($"Started dissolve effect for entity {entity.Index}", DebugLogManager.LogType.EnemyAnimation);
                }
                
                // Update dissolve amount if dissolving
                if (dissolveEffect.IsDissolving)
                {
                    float dissolveProgress = (currentTime - deathTimer.DissolveStartTime) / deathTimer.DissolveDuration;
                    dissolveEffect.DissolveAmount = math.clamp(dissolveProgress, 0f, 1f);
                    
                    // Destroy entity when dissolve is complete
                    if (dissolveEffect.DissolveAmount >= 1f)
                    {
                        ecb.DestroyEntity(entity);
                        
                        // Log for debugging
                        DebugLogManager.Instance.Log($"Destroyed entity {entity.Index} after dissolve effect", DebugLogManager.LogType.EnemyAnimation);
                    }
                }
            })
            .WithoutBurst() // Can't use Burst with DebugLogManager
            .Run();
    }
}
```

## Implementation Guidelines

When implementing or modifying health and damage systems, follow these guidelines:

1. **Health Management**:
   - Use a clear health component structure with current and max health
   - Implement damage resistance or vulnerability if needed
   - Add health regeneration for certain entity types
   - Consider implementing shields or armor as separate components

2. **Damage Application**:
   - Use events for damage to decouple damage sources from targets
   - Implement different damage types (physical, fire, etc.)
   - Add critical hit mechanics for increased damage
   - Consider area-of-effect damage for explosions

3. **Health Bar Display**:
   - Only show health bars for detected entities
   - Position health bars above entities with appropriate offset
   - Implement smooth health bar transitions for better visuals
   - Hide health bars for dead entities

4. **Death Handling**:
   - Play appropriate death animations
   - Implement dissolve effects for clean entity removal
   - Disable colliders and other components on death
   - Add death sound effects and particle systems

5. **Performance Considerations**:
   - Use entity command buffers for structural changes
   - Limit health bar updates to visible entities
   - Implement health bar pooling for better performance
   - Use Burst compilation where possible

6. **Configuration**:
   - Use ScriptableObjects for health system configuration
   - Centralize constants like spawn delay, dissolve duration, etc.
   - Make health bar appearance customizable
   - Allow for different death effects based on entity type
