# Player Modules and Controller Documentation

## Overview

The player system in this project follows a modular, event-driven architecture that separates different aspects of player functionality into independent modules. This design allows for high flexibility, maintainability, and extensibility while avoiding tight coupling between components.

## Architecture

The player system is built on the following architectural principles:

1. **Modular Design**: Each aspect of player functionality is encapsulated in a separate module
2. **Event-Driven Communication**: Modules communicate through an EventManager using subscribe/unsubscribe and broadcast patterns
3. **State Management**: A central StateManager handles state transitions and notifies relevant modules
4. **Scriptable Object Configuration**: Module parameters are configured through ScriptableObjects
5. **Interface-Based Programming**: Modules implement interfaces to ensure consistent behavior

## Core Components

### PlayerControllerBase

The `PlayerControllerBase` is the foundation of the player system, responsible for:

- Initializing and managing modules
- Updating modules based on their refresh rates and current state
- Maintaining references to player components

```csharp
public class PlayerControllerBase : SerializedMonoBehaviour
{
    public int ControllerIndex { get; private set; }
    protected List<IModule> _modules = new List<IModule>();
    public StateManager StateManager;
    
    // Initializes all modules and the state manager
    public void Initialize()
    
    // Adds a module to the controller
    public void AddModule(IModule module)
    
    // Removes a module from the controller
    public void RemoveModule(IModule module)
    
    // Updates all modules based on their refresh rates and current state
    protected void Update()
}
```

### StateManager

The `StateManager` handles state transitions and notifies modules of state changes:

```csharp
public class StateManager : SerializedMonoBehaviour
{
    private MainState currentMainState;
    private Dictionary<Type, Enum> subStates;
    
    // Gets the current state of a specific type
    public Enum GetState(Type enumType)
    
    // Sets the current main state
    public MainState CurrentMainState { get; set; }
    
    // Sets the current sub-state and notifies relevant modules
    public Enum CurrentSubState { set; }
    
    // Initializes the state manager with a list of modules
    public void Initialize(List<IModule> modules)
    
    // Handles sub-state changes and notifies relevant modules
    private void OnSubStateChange(Type stateType, Enum stateValue)
}
```

## Module System

### IModule Interface

All modules implement the `IModule` interface, which defines the core functionality:

```csharp
public interface IModule
{
    int ControllerIndex { get; }
    List<MainState> MainState { get; }
    void Initialize(int controllerIndex);
    void UpdateModule(MainState currentMainState, ref Enum currentSubState);
    bool HasRefreshRate { get; }
    float RefreshRate { get; }
    Enum GetModuleSubState();
}
```

### Key Modules

#### InputModule

The `InputModule` handles player input and distributes it to other modules:

- Processes raw input from input devices
- Converts input to game-relevant values
- Broadcasts input values to other modules through the event system
- Handles different input contexts (normal movement, aiming)

#### MovementModule

The `MovementModule` controls player movement and rotation:

- Processes input from the InputModule
- Handles different movement states (standing, walking, etc.)
- Controls character rotation
- Manages movement transitions
- Communicates with the AnimationModule for movement animations

Configuration is handled through `MovementModuleConfiguration` ScriptableObject:
- Movement parameters (speed, acceleration)
- Rotation settings
- Animation transition parameters

#### AimingModule

The `AimingModule` manages the player's aiming system:

- Detects potential targets within a configurable cone/sphere
- Selects the most appropriate target based on distance, angle, and other factors
- Controls upper body rotation for aiming
- Communicates with the MovementModule for whole-body rotation when needed

Configuration is handled through `AimingConfiguration` ScriptableObject:
- Detection parameters (cone angle, range)
- Target selection settings
- Aim assist parameters

#### AnimationModule

The `AnimationModule` handles all player animations:

- Manages animation states and transitions
- Provides a state machine for complex animation sequences
- Handles animation blending and layering
- Supports both Animator and Animancer animation systems

## State System

### Main States

The `MainState` enum defines the high-level states of the player:

```csharp
public enum MainState
{
    Normal,
    Combat,
    Stealth
}
```

### Sub-States

Each module has its own sub-state enum that defines more specific states:

```csharp
public enum MovementSubState
{
    Standing,
    WalkingStart,
    WalkingWithTurn,
    Stop,
    InPositionRotation
}

public enum WeaponSubState
{
    Equipping,
    UnEquipping,
    Idle,
    Aiming,
    Reloading,
    Shooting
}
```

## Communication Between Modules

Modules communicate through several mechanisms:

1. **Event System**: Modules broadcast and subscribe to events using the EventManager
2. **State Changes**: Modules can trigger state changes that are propagated to other modules
3. **Direct References**: Some modules may have direct references to other modules for performance-critical operations

### Example Event Flow

1. `InputModule` detects player input and broadcasts an input event
2. `MovementModule` receives the input event and updates player position/rotation
3. `MovementModule` changes its state to `WalkingStart`
4. `StateManager` notifies `AnimationModule` of the state change
5. `AnimationModule` plays the appropriate walking animation

## Configuration System

Module parameters are configured through ScriptableObjects, following the Scriptable Object pattern:

- `MovementModuleConfiguration`: Controls movement parameters
- `AimingConfiguration`: Controls aiming parameters

This approach allows for:
- Easy parameter tuning without code changes
- Multiple configuration presets
- Clear separation of data and logic

## Integration with ECS

The player system integrates with Unity's Entity Component System (ECS) for performance-critical operations:

- Player data is mirrored in ECS components
- Some systems (like target detection) use ECS for efficient processing
- Communication between MonoBehaviour modules and ECS systems is handled through specialized components

## Best Practices

When working with the player system:

1. **Follow the Module Pattern**: Create new modules for new functionality rather than extending existing ones
2. **Use Events for Communication**: Avoid direct references between modules when possible
3. **Configure Through ScriptableObjects**: Keep configuration parameters in ScriptableObjects
4. **Respect State Flow**: Use the state system for managing complex state transitions
5. **Document Module Interactions**: When modules need to interact, document the expected flow

## Common Workflows

### Adding a New Module

1. Create a new interface extending `IModule`
2. Implement the interface in a new class
3. Add the module to the PlayerController in the scene
4. Register for relevant events in the module's Initialize method

### Modifying Module Behavior

1. Identify the module responsible for the behavior
2. Modify the module's configuration ScriptableObject
3. If code changes are needed, update the module's UpdateModule method
4. Ensure state transitions are properly handled

### Debugging Module Interactions

1. Use the DebugLogManager to log state transitions and events
2. Check the StateManager's current states
3. Verify event subscriptions and broadcasts
4. Use Unity's debugger to step through module updates
