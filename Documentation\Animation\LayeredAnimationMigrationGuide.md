# Layered Animation Migration Guide

## Overview
This guide provides step-by-step instructions for migrating from the current layered animation system to the enhanced `UnifiedAnimationController` while maintaining modular lower body and upper body animation separation.

## Migration Benefits
- **Centralized Control**: All animation logic flows through UnifiedAnimationController
- **Maintained Modularity**: Lower body (movement) and upper body (weapon/action) animations remain separate
- **Backward Compatibility**: Existing WeaponAnimationModule calls continue to work
- **Simplified Workflow**: Fewer intermediate classes and clearer animation flow
- **Better Performance**: Reduced overhead from multiple animation managers

## Current vs New Architecture

### Current System:
```
MovementModule → CharacterAnimationModule → Animancer Base Layer
WeaponModule → WeaponAnimationModule → DynamicLayeredCharacterAnimations → LayeredAnimationManager → Animancer Action Layer
```

### New System:
```
MovementModule → UnifiedAnimationController → Animancer Base Layer (Lower Body)
WeaponModule → WeaponAnimationModule → UnifiedLayeredAnimationAdapter → UnifiedAnimationController → Animancer Action Layer (Upper Body)
```

## Step-by-Step Migration Instructions

### Phase 1: Setup Enhanced UnifiedAnimationController

1. **Configure UnifiedAnimationController in Inspector:**
   - Set `Enable Layered Animations` to `true`
   - Assign `Upper Body Mask` (your existing AvatarMask for upper body)
   - Set `Upper Body Fade Duration` (default: 0.25f)

2. **Verify Component Setup:**
   - Ensure `HybridAnimancerComponent` is attached
   - Ensure `CharacterAnimationModule` is attached
   - Ensure `WeaponAnimationModule` is attached

### Phase 2: Replace DynamicLayeredCharacterAnimations

1. **Add UnifiedLayeredAnimationAdapter:**
   ```csharp
   // Add this component to your character GameObject
   // It will automatically find and connect to UnifiedAnimationController
   ```

2. **Update WeaponAnimationModule References:**
   - Replace `DynamicLayeredCharacterAnimations` field with `UnifiedLayeredAnimationAdapter`
   - Update the field name in inspector

### Phase 3: Update Module Integration

1. **MovementModule Integration:**
   ```csharp
   // In MovementModule.cs, add reference to UnifiedAnimationController
   [SerializeField] private UnifiedAnimationController unifiedAnimationController;
   
   // Replace direct CharacterAnimationModule calls with:
   if (m_useAnimancer)
   {
       // For lower body animations (movement)
       unifiedAnimationController.PlayLowerBodyAnimation(animationTransition, 0.25f);
   }
   ```

2. **WeaponAnimationModule Integration:**
   ```csharp
   // In WeaponAnimationModule.cs, update the field:
   [SerializeField] private UnifiedLayeredAnimationAdapter layeredAnimationAdapter;
   
   // Existing calls remain the same:
   public void SetUpperBodyAnimation(ClipTransition animation)
   {
       layeredAnimationAdapter.PlayUpperBodyAnimation(animation);
   }
   ```

### Phase 4: Remove Deprecated Classes (Optional)

1. **Classes to Remove (after testing):**
   - `DynamicLayeredCharacterAnimations.cs`
   - Original `LayeredAnimationManager.cs` (if not used elsewhere)

2. **Classes to Keep:**
   - `UnifiedAnimationController.cs` (enhanced)
   - `UnifiedLayeredAnimationAdapter.cs` (new)
   - `CharacterAnimationModule.cs` (simplified usage)
   - `WeaponAnimationModule.cs` (minimal changes)

## Testing Checklist

### Lower Body Animations (Movement):
- [ ] Idle animations play correctly
- [ ] Walking/running transitions work
- [ ] Direction changes animate properly
- [ ] Stop animations function correctly

### Upper Body Animations (Weapons):
- [ ] Weapon aiming animations play on upper body only
- [ ] Shooting animations work correctly
- [ ] Upper body animations fade out properly
- [ ] Lower body continues movement during upper body actions

### Integration Testing:
- [ ] Moving while aiming works (lower + upper body)
- [ ] Animation transitions are smooth
- [ ] No animation conflicts between layers
- [ ] Performance is maintained or improved

## Troubleshooting

### Common Issues:

1. **Upper Body Mask Not Applied:**
   - Ensure `upperBodyMask` is assigned in UnifiedAnimationController
   - Verify the mask covers the correct bones

2. **Animations Not Playing:**
   - Check `enableLayeredAnimations` is true
   - Verify UnifiedAnimationController is initialized
   - Check console for initialization errors

3. **Layer Weight Issues:**
   - Use `GetUpperBodyWeight()` to debug layer weights
   - Ensure fade durations are appropriate

### Debug Methods:

```csharp
// Check if layered animations are working
bool isUpperBodyPlaying = unifiedAnimationController.IsUpperBodyAnimationPlaying();
float upperBodyWeight = unifiedAnimationController.GetUpperBodyWeight();
string animationInfo = unifiedAnimationController.GetCurrentAnimationInfo();
```

## Configuration Options

### UnifiedAnimationController Settings:
- `useAnimancer`: Enable/disable Animancer vs Animator Controller
- `enableLayeredAnimations`: Enable/disable layered animation system
- `upperBodyMask`: AvatarMask for upper body animations
- `upperBodyFadeDuration`: Default fade duration for upper body transitions
- `defaultTransitionDuration`: Default fade duration for all transitions
- `enableDebugLogging`: Enable detailed logging for debugging

## Performance Considerations

### Optimizations:
- Layered animations only initialize when `enableLayeredAnimations` is true
- Adapter pattern minimizes changes to existing code
- Centralized animation management reduces overhead
- Debug logging can be disabled in production

### Memory Usage:
- Minimal additional memory overhead
- Existing animation assets remain unchanged
- No duplication of animation data

## Next Steps

After successful migration:
1. Test all animation scenarios thoroughly
2. Remove deprecated classes if no longer needed
3. Consider implementing additional UnifiedAnimationController features
4. Update documentation for your team
5. Create animation presets using AnimationConfigurationManager
