# Performance Optimization Guide

This document provides specific guidance for optimizing performance in the project, with a focus on mobile platforms like the Amazon Fire HD 10. It complements the main ECS Technical Documentation by providing practical tips and strategies for improving framerate and reducing resource usage.

## Table of Contents

1. [Performance Benchmarks](#performance-benchmarks)
2. [Common Performance Issues](#common-performance-issues)
3. [Entity Count Optimization](#entity-count-optimization)
4. [Rendering Optimization](#rendering-optimization)
5. [Animation Optimization](#animation-optimization)
6. [Physics Optimization](#physics-optimization)
7. [Memory Optimization](#memory-optimization)
8. [Mobile-Specific Optimizations](#mobile-specific-optimizations)
9. [Monitoring and Profiling](#monitoring-and-profiling)
10. [Performance Checklist](#performance-checklist)

## Performance Benchmarks

Current performance metrics on target devices:

| Device | Entity Count | FPS | CPU Usage | GPU Usage | Memory Usage |
|--------|-------------|-----|-----------|-----------|--------------|
| Amazon Fire HD 10 (9th gen) | 100 | ~15 | High | High | Medium |
| Mid-range Android | 100 | ~30 | Medium | Medium | Low |
| High-end Android/iOS | 100 | 60+ | Low | Low | Low |

Target performance metrics:

| Device | Entity Count | Target FPS |
|--------|-------------|------------|
| Amazon Fire HD 10 (9th gen) | 100 | 30+ |
| Mid-range Android | 100 | 45+ |
| High-end Android/iOS | 100 | 60 |

## Common Performance Issues

### 1. Entity Overload

**Symptoms:**
- FPS drops when entity count increases
- CPU usage spikes during spawning
- Memory usage grows steadily

**Solutions:**
- Limit maximum entity count based on device capability
- Implement dynamic LOD system for entities
- Use entity pooling to reduce allocation overhead
- Implement distance-based culling for processing

### 2. Animation Overhead

**Symptoms:**
- FPS drops during animation transitions
- CPU spikes when many entities are animating
- Memory fragmentation over time

**Solutions:**
- Use GPU ECS Animator with optimized settings
- Reduce animation sample rate for distant entities
- Implement animation LOD system
- Disable animations for very distant entities

### 3. Physics Bottlenecks

**Symptoms:**
- FPS drops during collision-heavy scenarios
- CPU spikes during physics updates
- Inconsistent frame times

**Solutions:**
- Optimize collision shapes (use primitive colliders)
- Reduce physics update frequency for distant entities
- Use spatial partitioning to reduce collision checks
- Implement custom simplified physics for distant entities

### 4. Rendering Overhead

**Symptoms:**
- GPU bottlenecks
- FPS drops when many entities are visible
- High draw call count

**Solutions:**
- Implement mesh instancing for similar entities
- Use LOD groups for entities
- Reduce shader complexity for mobile
- Optimize texture sizes and formats

## Entity Count Optimization

The number of active entities has a significant impact on performance. Here are strategies to optimize entity count:

### Dynamic Entity Limit

Implement a system that adjusts the maximum entity count based on device performance:

```csharp
public class DynamicEntityLimiter : MonoBehaviour
{
    public float targetFrameRate = 30f;
    public int minEntityCount = 20;
    public int maxEntityCount = 100;
    public int adjustmentStep = 5;
    
    private float frameRateCheckInterval = 1f;
    private float timeSinceLastCheck = 0f;
    private int currentEntityLimit;
    
    private void Start()
    {
        // Start with a conservative limit
        currentEntityLimit = Mathf.FloorToInt((minEntityCount + maxEntityCount) / 2f);
        UpdateEntityLimit(currentEntityLimit);
    }
    
    private void Update()
    {
        timeSinceLastCheck += Time.deltaTime;
        
        if (timeSinceLastCheck >= frameRateCheckInterval)
        {
            float currentFPS = 1f / Time.smoothDeltaTime;
            
            // Adjust entity limit based on performance
            if (currentFPS < targetFrameRate * 0.8f)
            {
                // Performance is poor, reduce entity limit
                currentEntityLimit = Mathf.Max(minEntityCount, currentEntityLimit - adjustmentStep);
                UpdateEntityLimit(currentEntityLimit);
            }
            else if (currentFPS > targetFrameRate * 1.2f && currentEntityLimit < maxEntityCount)
            {
                // Performance is good, try increasing entity limit
                currentEntityLimit = Mathf.Min(maxEntityCount, currentEntityLimit + adjustmentStep);
                UpdateEntityLimit(currentEntityLimit);
            }
            
            timeSinceLastCheck = 0f;
        }
    }
    
    private void UpdateEntityLimit(int newLimit)
    {
        // Update the entity limit in the spawner system
        var spawnerSystem = World.DefaultGameObjectInjectionWorld.GetExistingSystem<CharacterSpawnerSystem>();
        spawnerSystem.SetMaxEntityCount(newLimit);
    }
}
```

### Entity Pooling

Implement entity pooling to reduce the overhead of creating and destroying entities:

```csharp
// In your spawner system
private NativeList<Entity> _entityPool;
private int _poolSize = 150; // Larger than max entities

public void OnCreate(ref SystemState state)
{
    _entityPool = new NativeList<Entity>(_poolSize, Allocator.Persistent);
    
    // Pre-populate pool
    var prefab = GetEntityPrefab();
    var entities = state.EntityManager.Instantiate(prefab, _poolSize, Allocator.Temp);
    
    for (int i = 0; i < entities.Length; i++)
    {
        // Deactivate entity
        state.EntityManager.SetComponentData(entities[i], new ActiveTag { Value = false });
        _entityPool.Add(entities[i]);
    }
}

public Entity GetEntityFromPool()
{
    if (_entityPool.Length > 0)
    {
        var entity = _entityPool[_entityPool.Length - 1];
        _entityPool.RemoveAt(_entityPool.Length - 1);
        
        // Activate entity
        EntityManager.SetComponentData(entity, new ActiveTag { Value = true });
        return entity;
    }
    
    // Pool is empty, create new entity
    return EntityManager.Instantiate(GetEntityPrefab());
}

public void ReturnEntityToPool(Entity entity)
{
    // Deactivate entity
    EntityManager.SetComponentData(entity, new ActiveTag { Value = false });
    
    // Return to pool if not full
    if (_entityPool.Length < _poolSize)
    {
        _entityPool.Add(entity);
    }
    else
    {
        // Pool is full, destroy entity
        EntityManager.DestroyEntity(entity);
    }
}
```

### Distance-Based Processing

Implement distance-based processing to reduce CPU usage for distant entities:

```csharp
[BurstCompile]
private partial struct UpdateEntitiesJob : IJobEntity
{
    [ReadOnly] public float3 PlayerPosition;
    [ReadOnly] public float FullProcessingRadius;
    [ReadOnly] public float ReducedProcessingRadius;
    
    void Execute(Entity entity, [EntityIndexInQuery] int sortKey,
                ref ProcessingLevelComponent processingLevel,
                in LocalTransform transform)
    {
        float distanceSq = math.distancesq(transform.Position, PlayerPosition);
        
        if (distanceSq <= FullProcessingRadius * FullProcessingRadius)
        {
            // Full processing for close entities
            processingLevel.Level = ProcessingLevel.Full;
        }
        else if (distanceSq <= ReducedProcessingRadius * ReducedProcessingRadius)
        {
            // Reduced processing for medium-distance entities
            processingLevel.Level = ProcessingLevel.Reduced;
        }
        else
        {
            // Minimal processing for distant entities
            processingLevel.Level = ProcessingLevel.Minimal;
        }
    }
}
```

## Rendering Optimization

### GPU Instancing

Enable GPU instancing for similar entities to reduce draw calls:

1. Ensure materials have "Enable GPU Instancing" checked
2. Group similar entities together in the scene
3. Use MaterialPropertyBlocks for variations

### Mesh LOD

Implement mesh LOD (Level of Detail) for entities:

1. Create multiple LOD levels for character meshes
2. Configure LOD Group component with appropriate distances
3. Adjust LOD distances based on device performance

```csharp
// Adjust LOD distances based on device performance
public void OptimizeLODForDevice()
{
    // Get all LOD groups
    LODGroup[] lodGroups = FindObjectsOfType<LODGroup>();
    
    // Determine performance tier
    PerformanceTier tier = GetDevicePerformanceTier();
    
    float lodBias = tier switch
    {
        PerformanceTier.Low => 0.5f,    // Aggressive LOD reduction for low-end devices
        PerformanceTier.Medium => 0.75f, // Moderate LOD reduction for mid-range devices
        PerformanceTier.High => 1.0f,    // Normal LOD settings for high-end devices
        _ => 0.75f
    };
    
    // Apply LOD bias
    QualitySettings.lodBias = lodBias;
    
    // Optionally adjust individual LOD groups
    foreach (var lodGroup in lodGroups)
    {
        LOD[] lods = lodGroup.GetLODs();
        
        for (int i = 0; i < lods.Length; i++)
        {
            // Adjust transition distances based on performance tier
            lods[i].screenRelativeTransitionHeight *= lodBias;
        }
        
        lodGroup.SetLODs(lods);
    }
}
```

### Shader Optimization

Optimize shaders for mobile devices:

1. Use mobile-friendly shader variants
2. Reduce texture sizes and use compression
3. Minimize shader complexity and instructions
4. Use shader LOD for distant entities

## Animation Optimization

### GPU ECS Animator Optimization

Optimize GPU ECS Animator settings for better performance:

```csharp
// Configure GPU ECS Animator for optimal performance
public void OptimizeGPUAnimator()
{
    // Find all GPU ECS Animator components
    var animators = FindObjectsOfType<GpuEcsAnimatorBehaviour>();
    
    foreach (var animator in animators)
    {
        // Reduce animation sample rate for better performance
        animator.SampleRate = 30; // 30 fps animations instead of 60
        
        // Optimize memory usage
        animator.OptimizeMemoryUsage = true;
        
        // Reduce animation blend time for faster transitions
        animator.DefaultBlendTime = 0.1f;
    }
}
```

### Animation LOD

Implement animation LOD to reduce animation processing for distant entities:

```csharp
[BurstCompile]
private partial struct AnimationLODJob : IJobEntity
{
    [ReadOnly] public float3 PlayerPosition;
    [ReadOnly] public float FullAnimationRadius;
    [ReadOnly] public float ReducedAnimationRadius;
    
    void Execute(Entity entity, [EntityIndexInQuery] int sortKey,
                ref AnimationLODComponent animLOD,
                in LocalTransform transform)
    {
        float distanceSq = math.distancesq(transform.Position, PlayerPosition);
        
        if (distanceSq <= FullAnimationRadius * FullAnimationRadius)
        {
            // Full animation for close entities
            animLOD.SampleRate = 1; // Every frame
        }
        else if (distanceSq <= ReducedAnimationRadius * ReducedAnimationRadius)
        {
            // Reduced animation for medium-distance entities
            animLOD.SampleRate = 2; // Every other frame
        }
        else
        {
            // Minimal animation for distant entities
            animLOD.SampleRate = 4; // Every fourth frame
        }
    }
}
```

### Animation Culling

Implement animation culling to disable animations for off-screen entities:

```csharp
// In your animation system
protected override void OnUpdate()
{
    // Get main camera
    Camera mainCamera = Camera.main;
    if (mainCamera == null) return;
    
    // Create frustum planes
    Plane[] frustumPlanes = GeometryUtility.CalculateFrustumPlanes(mainCamera);
    
    // Create job to update animation visibility
    var animCullingJob = new AnimationCullingJob
    {
        FrustumPlanes = new NativeArray<float4>(6, Allocator.TempJob),
        CullDistance = 50f // Don't animate entities beyond this distance
    };
    
    // Convert Unity planes to float4 for the job
    for (int i = 0; i < 6; i++)
    {
        Vector3 normal = frustumPlanes[i].normal;
        float distance = frustumPlanes[i].distance;
        animCullingJob.FrustumPlanes[i] = new float4(normal.x, normal.y, normal.z, distance);
    }
    
    // Schedule the job
    var handle = animCullingJob.Schedule(this.Dependency);
    
    // Clean up
    handle.Complete();
    animCullingJob.FrustumPlanes.Dispose();
}
```

## Physics Optimization

### Collision Optimization

Optimize collision detection for better performance:

1. Use primitive colliders (sphere, capsule) instead of mesh colliders
2. Implement layers to control what collides with what
3. Use compound colliders for complex shapes
4. Disable colliders for distant entities

### Spatial Partitioning

Use spatial partitioning to reduce collision checks:

```csharp
// In KDTreeSystem
[BurstCompile]
public void OnUpdate(ref SystemState state)
{
    // Only rebuild the tree periodically
    _rebuildTimer += SystemAPI.Time.DeltaTime;
    if (_rebuildTimer < _rebuildInterval) return;
    _rebuildTimer = 0;
    
    // Get all enemy positions
    var enemyPositions = new NativeList<float3>(1000, Allocator.TempJob);
    var enemyEntities = new NativeList<Entity>(1000, Allocator.TempJob);
    
    foreach (var (transform, entity) in 
             SystemAPI.Query<RefRO<LocalTransform>>()
                     .WithAll<EnemyTag>()
                     .WithEntityAccess())
    {
        enemyPositions.Add(transform.ValueRO.Position);
        enemyEntities.Add(entity);
    }
    
    // Build KD-tree
    var kdTree = new KDTree(enemyPositions, Allocator.TempJob);
    
    // Store tree in singleton component
    var kdTreeSingleton = SystemAPI.GetSingletonRW<KDTreeSingleton>();
    if (kdTreeSingleton.ValueRO.Tree.IsCreated)
    {
        kdTreeSingleton.ValueRO.Tree.Dispose();
    }
    
    kdTreeSingleton.ValueRW.Tree = kdTree;
    kdTreeSingleton.ValueRW.Entities = new NativeArray<Entity>(enemyEntities.ToArray(), Allocator.Persistent);
    
    // Clean up
    enemyPositions.Dispose();
    enemyEntities.Dispose();
}
```

### Physics Rate Adjustment

Adjust physics update rate based on distance:

```csharp
[BurstCompile]
private partial struct PhysicsRateJob : IJobEntity
{
    [ReadOnly] public float3 PlayerPosition;
    [ReadOnly] public float FullPhysicsRadius;
    [ReadOnly] public float ReducedPhysicsRadius;
    [ReadOnly] public int FrameCount;
    
    void Execute(Entity entity, [EntityIndexInQuery] int sortKey,
                ref PhysicsRateComponent physicsRate,
                in LocalTransform transform)
    {
        float distanceSq = math.distancesq(transform.Position, PlayerPosition);
        
        if (distanceSq <= FullPhysicsRadius * FullPhysicsRadius)
        {
            // Full physics for close entities
            physicsRate.UpdateInterval = 1; // Every frame
            physicsRate.ShouldUpdate = true;
        }
        else if (distanceSq <= ReducedPhysicsRadius * ReducedPhysicsRadius)
        {
            // Reduced physics for medium-distance entities
            physicsRate.UpdateInterval = 2; // Every other frame
            physicsRate.ShouldUpdate = FrameCount % physicsRate.UpdateInterval == 0;
        }
        else
        {
            // Minimal physics for distant entities
            physicsRate.UpdateInterval = 4; // Every fourth frame
            physicsRate.ShouldUpdate = FrameCount % physicsRate.UpdateInterval == 0;
        }
    }
}
```

## Memory Optimization

### Texture Compression

Optimize textures for mobile devices:

1. Use appropriate compression formats (ASTC for modern devices)
2. Reduce texture sizes for mobile (1024x1024 max for characters)
3. Use texture atlasing to reduce draw calls
4. Implement mipmap streaming

### Asset Bundle Optimization

Optimize asset bundles for better loading performance:

1. Group related assets together
2. Use asset bundle compression
3. Implement asset bundle caching
4. Use addressable assets for better memory management

### Memory Pooling

Implement memory pooling for frequently allocated objects:

```csharp
// Native collection pooling
public static class NativeCollectionPool
{
    private static Dictionary<int, Stack<NativeList<float3>>> _float3ListPool = new Dictionary<int, Stack<NativeList<float3>>>();
    
    public static NativeList<float3> GetFloat3List(int capacity)
    {
        if (!_float3ListPool.TryGetValue(capacity, out var stack))
        {
            stack = new Stack<NativeList<float3>>();
            _float3ListPool[capacity] = stack;
        }
        
        if (stack.Count > 0)
        {
            return stack.Pop();
        }
        
        return new NativeList<float3>(capacity, Allocator.Persistent);
    }
    
    public static void ReturnFloat3List(NativeList<float3> list)
    {
        int capacity = list.Capacity;
        
        list.Clear();
        
        if (!_float3ListPool.TryGetValue(capacity, out var stack))
        {
            stack = new Stack<NativeList<float3>>();
            _float3ListPool[capacity] = stack;
        }
        
        stack.Push(list);
    }
    
    public static void Dispose()
    {
        foreach (var stack in _float3ListPool.Values)
        {
            while (stack.Count > 0)
            {
                var list = stack.Pop();
                if (list.IsCreated)
                {
                    list.Dispose();
                }
            }
        }
        
        _float3ListPool.Clear();
    }
}
```

## Mobile-Specific Optimizations

### Thermal Throttling Management

Implement thermal throttling detection and management:

```csharp
public class ThermalManager : MonoBehaviour
{
    public float checkInterval = 5f;
    private float timeSinceLastCheck = 0f;
    
    private float baselineFrameRate;
    private bool throttlingDetected = false;
    
    private void Start()
    {
        // Record baseline frame rate
        baselineFrameRate = 1f / Time.smoothDeltaTime;
    }
    
    private void Update()
    {
        timeSinceLastCheck += Time.deltaTime;
        
        if (timeSinceLastCheck >= checkInterval)
        {
            float currentFrameRate = 1f / Time.smoothDeltaTime;
            
            // Check for significant frame rate drop
            if (currentFrameRate < baselineFrameRate * 0.7f && !throttlingDetected)
            {
                // Thermal throttling likely detected
                throttlingDetected = true;
                ApplyThrottlingMitigations();
            }
            else if (currentFrameRate > baselineFrameRate * 0.9f && throttlingDetected)
            {
                // Device has recovered
                throttlingDetected = false;
                RestoreNormalSettings();
            }
            
            timeSinceLastCheck = 0f;
        }
    }
    
    private void ApplyThrottlingMitigations()
    {
        // Reduce entity count
        var spawnerSystem = World.DefaultGameObjectInjectionWorld.GetExistingSystem<CharacterSpawnerSystem>();
        spawnerSystem.SetMaxEntityCount(spawnerSystem.GetMaxEntityCount() / 2);
        
        // Reduce graphics quality
        QualitySettings.SetQualityLevel(0, true); // Lowest quality
        
        // Reduce animation sample rate
        var animSystem = World.DefaultGameObjectInjectionWorld.GetExistingSystem<EnemyAnimationSystem>();
        animSystem.SetGlobalAnimationRate(2); // Every other frame
        
        // Disable non-essential effects
        DisableNonEssentialEffects();
    }
    
    private void RestoreNormalSettings()
    {
        // Restore entity count gradually
        var spawnerSystem = World.DefaultGameObjectInjectionWorld.GetExistingSystem<CharacterSpawnerSystem>();
        spawnerSystem.SetMaxEntityCount(Mathf.Min(spawnerSystem.GetMaxEntityCount() * 1.2f, spawnerSystem.GetDefaultMaxEntityCount()));
        
        // Restore graphics quality
        QualitySettings.SetQualityLevel(1, true); // Medium quality
        
        // Restore animation sample rate
        var animSystem = World.DefaultGameObjectInjectionWorld.GetExistingSystem<EnemyAnimationSystem>();
        animSystem.SetGlobalAnimationRate(1); // Every frame
        
        // Re-enable effects
        EnableNonEssentialEffects();
    }
    
    private void DisableNonEssentialEffects()
    {
        // Disable particle effects, post-processing, etc.
    }
    
    private void EnableNonEssentialEffects()
    {
        // Re-enable effects based on device capability
    }
}
```

### Battery Optimization

Implement battery-aware optimizations:

```csharp
public class BatteryAwareOptimizer : MonoBehaviour
{
    public float checkInterval = 30f;
    private float timeSinceLastCheck = 0f;
    
    private void Update()
    {
        timeSinceLastCheck += Time.deltaTime;
        
        if (timeSinceLastCheck >= checkInterval)
        {
            // Check battery level on supported platforms
            #if UNITY_ANDROID
            try
            {
                AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
                AndroidJavaObject intentFilter = new AndroidJavaObject("android.content.IntentFilter", "android.intent.action.BATTERY_CHANGED");
                AndroidJavaObject batteryStatus = currentActivity.Call<AndroidJavaObject>("registerReceiver", null, intentFilter);
                
                int level = batteryStatus.Call<int>("getIntExtra", "level", -1);
                int scale = batteryStatus.Call<int>("getIntExtra", "scale", -1);
                
                float batteryPct = level / (float)scale;
                
                // Apply optimizations based on battery level
                if (batteryPct < 0.2f)
                {
                    // Critical battery level
                    ApplyAggressiveOptimizations();
                }
                else if (batteryPct < 0.5f)
                {
                    // Low battery level
                    ApplyModerateOptimizations();
                }
                else
                {
                    // Good battery level
                    ApplyNormalSettings();
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("Error getting battery status: " + e.Message);
            }
            #endif
            
            timeSinceLastCheck = 0f;
        }
    }
    
    private void ApplyAggressiveOptimizations()
    {
        // Reduce entity count significantly
        // Disable non-essential systems
        // Lower frame rate target
        Application.targetFrameRate = 30;
    }
    
    private void ApplyModerateOptimizations()
    {
        // Reduce entity count moderately
        // Optimize system update rates
        // Balance frame rate
        Application.targetFrameRate = 45;
    }
    
    private void ApplyNormalSettings()
    {
        // Use default entity count
        // Normal system update rates
        // Target full frame rate
        Application.targetFrameRate = 60;
    }
}
```

### Input Optimization

Optimize input handling for mobile devices:

1. Reduce input polling frequency
2. Use touch input pooling
3. Implement gesture recognition on the CPU instead of in ECS
4. Batch input events before sending to ECS

## Monitoring and Profiling

### Graphy Integration

Configure Graphy for optimal performance monitoring:

1. Enable FPS, RAM, and Audio modules
2. Configure warning thresholds for your target devices
3. Use the advanced module for detailed system information
4. Add custom monitors for ECS-specific metrics

```csharp
// Configure Graphy for performance monitoring
public void ConfigureGraphy()
{
    var graphyManager = FindObjectOfType<GraphyManager>();
    if (graphyManager == null) return;
    
    // Set up FPS monitoring
    graphyManager.FpsModuleState = GraphyManager.ModuleState.FULL;
    graphyManager.GoodFPSThreshold = 40;
    graphyManager.CautionFPSThreshold = 30;
    
    // Set up RAM monitoring
    graphyManager.RamModuleState = GraphyManager.ModuleState.FULL;
    
    // Set up advanced monitoring
    graphyManager.AdvancedModuleState = GraphyManager.ModuleState.FULL;
    
    // Position the graph
    graphyManager.GraphModulePosition = GraphyManager.ModulePosition.TOP_RIGHT;
}
```

### Custom Performance Monitors

Implement custom performance monitors for ECS-specific metrics:

```csharp
public class ECSPerformanceMonitor : MonoBehaviour
{
    [Header("Monitoring Settings")]
    public bool enableMonitoring = true;
    public float updateInterval = 0.5f;
    
    [Header("Display Settings")]
    public bool showEntityCount = true;
    public bool showSystemTimes = true;
    public bool showMemoryUsage = true;
    
    [Header("References")]
    public Text entityCountText;
    public Text systemTimesText;
    public Text memoryUsageText;
    
    private float timeSinceLastUpdate = 0f;
    private World defaultWorld;
    
    private void Start()
    {
        defaultWorld = World.DefaultGameObjectInjectionWorld;
    }
    
    private void Update()
    {
        if (!enableMonitoring) return;
        
        timeSinceLastUpdate += Time.deltaTime;
        
        if (timeSinceLastUpdate >= updateInterval)
        {
            UpdateMonitors();
            timeSinceLastUpdate = 0f;
        }
    }
    
    private void UpdateMonitors()
    {
        if (defaultWorld == null) return;
        
        // Update entity count display
        if (showEntityCount && entityCountText != null)
        {
            int entityCount = defaultWorld.EntityManager.UniversalQuery.CalculateEntityCount();
            entityCountText.text = $"Entities: {entityCount}";
        }
        
        // Update system times display
        if (showSystemTimes && systemTimesText != null)
        {
            var systemTimes = new System.Text.StringBuilder();
            
            // Get top 5 systems by execution time
            var systems = defaultWorld.Systems.OrderByDescending(s => s.GetType().GetField("m_LastExecutionTime")?.GetValue(s) as float? ?? 0f).Take(5);
            
            foreach (var system in systems)
            {
                float executionTime = system.GetType().GetField("m_LastExecutionTime")?.GetValue(system) as float? ?? 0f;
                systemTimes.AppendLine($"{system.GetType().Name}: {executionTime:F3}ms");
            }
            
            systemTimesText.text = systemTimes.ToString();
        }
        
        // Update memory usage display
        if (showMemoryUsage && memoryUsageText != null)
        {
            long totalMemory = Profiler.GetTotalAllocatedMemoryLong() / (1024 * 1024); // MB
            memoryUsageText.text = $"Memory: {totalMemory}MB";
        }
    }
}
```

## Performance Checklist

Use this checklist when optimizing performance for mobile devices:

### Entity Management
- [ ] Limit maximum entity count based on device capability
- [ ] Implement entity pooling to reduce allocation overhead
- [ ] Use distance-based processing for entities
- [ ] Implement entity culling for off-screen entities

### Rendering
- [ ] Enable GPU instancing for similar entities
- [ ] Implement mesh LOD for entities
- [ ] Optimize shaders for mobile devices
- [ ] Reduce draw calls through batching and atlasing

### Animation
- [ ] Optimize GPU ECS Animator settings
- [ ] Implement animation LOD for distant entities
- [ ] Disable animations for off-screen entities
- [ ] Reduce animation sample rate for better performance

### Physics
- [ ] Use primitive colliders instead of mesh colliders
- [ ] Implement spatial partitioning for collision detection
- [ ] Adjust physics update rate based on distance
- [ ] Disable colliders for distant entities

### Memory
- [ ] Optimize textures for mobile devices
- [ ] Implement memory pooling for frequently allocated objects
- [ ] Use asset bundle optimization for better loading
- [ ] Monitor and manage memory usage

### Mobile-Specific
- [ ] Implement thermal throttling detection and management
- [ ] Apply battery-aware optimizations
- [ ] Optimize input handling for mobile devices
- [ ] Adjust quality settings based on device capability

### Monitoring
- [ ] Configure Graphy for performance monitoring
- [ ] Implement custom performance monitors for ECS
- [ ] Regularly profile the application on target devices
- [ ] Track performance metrics over time
