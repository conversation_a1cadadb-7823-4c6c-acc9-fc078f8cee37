# ECS Technical Documentation - Part 8: Debugging and Implementation Guidelines

## Introduction

This document provides guidance on debugging ECS systems and implementing new features in the project. It covers logging, visualization, performance monitoring, and best practices for extending the existing architecture.

## Table of Contents

1. [Debugging Tools](#debugging-tools)
2. [Logging System](#logging-system)
3. [Performance Monitoring](#performance-monitoring)
4. [Implementation Guidelines](#implementation-guidelines)
5. [Common Issues and Solutions](#common-issues-and-solutions)

## Debugging Tools

### Unity ECS Debug Tools

Unity provides several tools for debugging ECS systems:

1. **Entity Debugger**:
   - Access via Window > DOTS > Entities > Entity Debugger
   - View all entities, components, and systems
   - Filter entities by archetype or component
   - Inspect component values
   - Enable/disable systems

2. **System Scheduler Window**:
   - Access via Window > DOTS > Entities > Systems
   - View system execution order
   - See system dependencies
   - Monitor system execution time

3. **Memory Profiler**:
   - Access via Window > Analysis > Memory Profiler
   - Analyze memory usage of ECS components
   - Identify memory leaks
   - Track native container allocations

4. **<PERSON>urst Inspector**:
   - Access via Window > Burst > Burst Inspector
   - View Burst-compiled code
   - Check for compilation issues
   - Analyze performance optimizations

### Custom Debug Visualization

The project includes custom visualization tools for debugging:

```csharp
// Debug visualization system
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class DebugVisualizationSystem : SystemBase
{
    private bool showDetectionRadius = false;
    private bool showSpatialPartitioning = false;
    private bool showPathfinding = false;

    protected override void OnUpdate()
    {
        if (!DebugManager.Instance.IsDebugEnabled)
            return;
            
        showDetectionRadius = DebugManager.Instance.ShowDetectionRadius;
        showSpatialPartitioning = DebugManager.Instance.ShowSpatialPartitioning;
        showPathfinding = DebugManager.Instance.ShowPathfinding;
        
        if (showDetectionRadius)
        {
            // Draw detection radius for all sensors
            Entities
                .WithAll<SphereDetectSensorComponent>()
                .ForEach((in SphereDetectSensorComponent sensor, in LocalToWorld transform) =>
                {
                    // Draw detection radius
                    Debug.DrawWireSphere(transform.Position, sensor.DetectionRadius, Color.yellow);
                    
                    // Draw detection cone
                    DrawDetectionCone(transform.Position, transform.Forward, sensor.DetectionAngle, sensor.DetectionRange, Color.red);
                })
                .WithoutBurst() // Can't use Burst with Debug.DrawWireSphere
                .Run();
        }
        
        if (showSpatialPartitioning)
        {
            // Draw spatial partitioning grid
            if (!gridQuery.IsEmpty)
            {
                var gridEntity = gridQuery.GetSingletonEntity();
                var grid = EntityManager.GetComponentData<SpatialHashGridComponent>(gridEntity);
                var cells = EntityManager.GetBuffer<GridCell>(gridEntity);
                
                DrawSpatialGrid(grid, cells);
            }
            
            // Draw KD-tree nodes
            if (!kdTreeQuery.IsEmpty)
            {
                var kdTreeEntity = kdTreeQuery.GetSingletonEntity();
                var kdTree = EntityManager.GetComponentData<KDTreeComponent>(kdTreeEntity);
                
                DrawKDTree(kdTree);
            }
        }
        
        if (showPathfinding)
        {
            // Draw pathfinding paths
            Entities
                .WithAll<AgentBody>()
                .ForEach((in AgentBody body, in LocalToWorld transform) =>
                {
                    // Draw path to destination
                    Debug.DrawLine(transform.Position, body.Destination, Color.blue);
                })
                .WithoutBurst() // Can't use Burst with Debug.DrawLine
                .Run();
        }
    }
    
    private void DrawDetectionCone(float3 position, float3 forward, float angle, float range, Color color)
    {
        // Draw detection cone visualization
    }
    
    private void DrawSpatialGrid(SpatialHashGridComponent grid, DynamicBuffer<GridCell> cells)
    {
        // Draw spatial grid visualization
    }
    
    private void DrawKDTree(KDTreeComponent kdTree)
    {
        // Draw KD-tree visualization
    }
}
```

## Logging System

The project uses a custom logging system to control log output and categorize logs by type.

### DebugLogManager

```csharp
public class DebugLogManager : MonoBehaviour
{
    public enum LogType
    {
        INFO,
        WARNING,
        ERROR,
        DETECTION,
        TARGETING,
        WEAPON,
        HEALTH,
        ENEMY,
        SPAWNER,
        ANIMATION,
        EnemyAnimation,
        PERFORMANCE
    }
    
    [Serializable]
    public class LogTypeSettings
    {
        public LogType Type;
        public bool Enabled;
        public Color Color;
    }
    
    [SerializeField] private List<LogTypeSettings> logTypeSettings = new List<LogTypeSettings>();
    [SerializeField] private bool logToConsole = true;
    [SerializeField] private bool logToFile = false;
    [SerializeField] private string logFilePath = "Logs/ecs_log.txt";
    
    private Dictionary<LogType, LogTypeSettings> logTypeMap = new Dictionary<LogType, LogTypeSettings>();
    private StreamWriter logFileWriter;
    
    // Singleton instance
    public static DebugLogManager Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            
            // Initialize log type map
            foreach (var setting in logTypeSettings)
            {
                logTypeMap[setting.Type] = setting;
            }
            
            // Initialize log file
            if (logToFile)
            {
                try
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(logFilePath));
                    logFileWriter = new StreamWriter(logFilePath, true);
                    logFileWriter.WriteLine($"=== Log started at {DateTime.Now} ===");
                    logFileWriter.Flush();
                }
                catch (Exception e)
                {
                    Debug.LogError($"Failed to create log file: {e.Message}");
                    logToFile = false;
                }
            }
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void OnDestroy()
    {
        if (Instance == this)
        {
            if (logFileWriter != null)
            {
                logFileWriter.WriteLine($"=== Log ended at {DateTime.Now} ===");
                logFileWriter.Close();
                logFileWriter = null;
            }
            
            Instance = null;
        }
    }
    
    public void Log(string message, LogType type = LogType.INFO)
    {
        if (!IsLogTypeEnabled(type))
            return;
            
        var setting = logTypeMap.ContainsKey(type) ? logTypeMap[type] : null;
        var color = setting != null ? setting.Color : Color.white;
        var typeStr = type.ToString();
        
        var formattedMessage = $"[{typeStr}] {message}";
        
        if (logToConsole)
        {
            if (setting != null && color != Color.white)
            {
                var hexColor = ColorUtility.ToHtmlStringRGB(color);
                Debug.Log($"<color=#{hexColor}>{formattedMessage}</color>");
            }
            else
            {
                Debug.Log(formattedMessage);
            }
        }
        
        if (logToFile && logFileWriter != null)
        {
            logFileWriter.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] {formattedMessage}");
            logFileWriter.Flush();
        }
    }
    
    public bool IsLogTypeEnabled(LogType type)
    {
        return logTypeMap.ContainsKey(type) && logTypeMap[type].Enabled;
    }
    
    public void SetLogTypeEnabled(LogType type, bool enabled)
    {
        if (logTypeMap.ContainsKey(type))
        {
            logTypeMap[type].Enabled = enabled;
        }
    }
}
```

### Custom Editor for DebugLogManager

```csharp
[CustomEditor(typeof(DebugLogManager))]
public class DebugLogManagerEditor : Editor
{
    private SerializedProperty logTypeSettingsProp;
    private SerializedProperty logToConsoleProp;
    private SerializedProperty logToFileProp;
    private SerializedProperty logFilePathProp;
    
    private void OnEnable()
    {
        logTypeSettingsProp = serializedObject.FindProperty("logTypeSettings");
        logToConsoleProp = serializedObject.FindProperty("logToConsole");
        logToFileProp = serializedObject.FindProperty("logToFile");
        logFilePathProp = serializedObject.FindProperty("logFilePath");
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        EditorGUILayout.PropertyField(logToConsoleProp);
        EditorGUILayout.PropertyField(logToFileProp);
        
        if (logToFileProp.boolValue)
        {
            EditorGUILayout.PropertyField(logFilePathProp);
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Log Types", EditorStyles.boldLabel);
        
        // Add buttons to enable/disable all
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Enable All"))
        {
            for (int i = 0; i < logTypeSettingsProp.arraySize; i++)
            {
                var element = logTypeSettingsProp.GetArrayElementAtIndex(i);
                element.FindPropertyRelative("Enabled").boolValue = true;
            }
        }
        
        if (GUILayout.Button("Disable All"))
        {
            for (int i = 0; i < logTypeSettingsProp.arraySize; i++)
            {
                var element = logTypeSettingsProp.GetArrayElementAtIndex(i);
                element.FindPropertyRelative("Enabled").boolValue = false;
            }
        }
        EditorGUILayout.EndHorizontal();
        
        // Draw log type settings
        for (int i = 0; i < logTypeSettingsProp.arraySize; i++)
        {
            var element = logTypeSettingsProp.GetArrayElementAtIndex(i);
            var typeProp = element.FindPropertyRelative("Type");
            var enabledProp = element.FindPropertyRelative("Enabled");
            var colorProp = element.FindPropertyRelative("Color");
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.PropertyField(typeProp, GUIContent.none, GUILayout.Width(100));
            enabledProp.boolValue = EditorGUILayout.Toggle(enabledProp.boolValue, GUILayout.Width(20));
            EditorGUILayout.PropertyField(colorProp, GUIContent.none);
            EditorGUILayout.EndHorizontal();
        }
        
        serializedObject.ApplyModifiedProperties();
    }
}
```

### Using the Logging System

```csharp
// Example of using the logging system
public void ProcessEntity(Entity entity)
{
    // Log with default INFO type
    DebugLogManager.Instance.Log($"Processing entity {entity.Index}");
    
    // Log with specific type
    DebugLogManager.Instance.Log($"Entity {entity.Index} detected", DebugLogManager.LogType.DETECTION);
    
    // Check if log type is enabled before doing expensive string formatting
    if (DebugLogManager.Instance.IsLogTypeEnabled(DebugLogManager.LogType.PERFORMANCE))
    {
        var stats = CalculatePerformanceStats();
        DebugLogManager.Instance.Log($"Performance stats: {stats}", DebugLogManager.LogType.PERFORMANCE);
    }
}
```

## Performance Monitoring

The project includes a performance monitoring system to track and visualize performance metrics.

### PerformanceMonitor

```csharp
public class PerformanceMonitor : MonoBehaviour
{
    [SerializeField] private float updateInterval = 0.5f;
    [SerializeField] private int frameBufferSize = 60;
    
    private float[] fpsBuffer;
    private int bufferIndex = 0;
    private float fpsAccumulator = 0f;
    private int frameCounter = 0;
    private float timeCounter = 0f;
    
    private float currentFPS = 0f;
    private float averageFPS = 0f;
    private float minFPS = float.MaxValue;
    private float maxFPS = 0f;
    
    private int entityCount = 0;
    private int systemCount = 0;
    
    // Singleton instance
    public static PerformanceMonitor Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            
            fpsBuffer = new float[frameBufferSize];
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Update()
    {
        // Update FPS counter
        timeCounter += Time.unscaledDeltaTime;
        frameCounter++;
        
        if (timeCounter >= updateInterval)
        {
            // Calculate current FPS
            currentFPS = frameCounter / timeCounter;
            
            // Update buffer
            fpsBuffer[bufferIndex] = currentFPS;
            bufferIndex = (bufferIndex + 1) % frameBufferSize;
            
            // Calculate statistics
            CalculateStatistics();
            
            // Update entity and system counts
            UpdateEntitySystemCounts();
            
            // Reset counters
            timeCounter = 0f;
            frameCounter = 0;
            
            // Log performance
            LogPerformance();
        }
    }
    
    private void CalculateStatistics()
    {
        float sum = 0f;
        minFPS = float.MaxValue;
        maxFPS = 0f;
        
        for (int i = 0; i < frameBufferSize; i++)
        {
            if (fpsBuffer[i] == 0)
                continue;
                
            sum += fpsBuffer[i];
            minFPS = math.min(minFPS, fpsBuffer[i]);
            maxFPS = math.max(maxFPS, fpsBuffer[i]);
        }
        
        averageFPS = sum / frameBufferSize;
    }
    
    private void UpdateEntitySystemCounts()
    {
        var world = World.DefaultGameObjectInjectionWorld;
        if (world != null)
        {
            entityCount = world.EntityManager.UniversalQuery.CalculateEntityCount();
            systemCount = world.Systems.Count;
        }
    }
    
    private void LogPerformance()
    {
        if (DebugLogManager.Instance != null && 
            DebugLogManager.Instance.IsLogTypeEnabled(DebugLogManager.LogType.PERFORMANCE))
        {
            DebugLogManager.Instance.Log(
                $"FPS: {currentFPS:F1} (Avg: {averageFPS:F1}, Min: {minFPS:F1}, Max: {maxFPS:F1}) | " +
                $"Entities: {entityCount} | Systems: {systemCount}",
                DebugLogManager.LogType.PERFORMANCE
            );
        }
    }
    
    // Public accessors
    public float CurrentFPS => currentFPS;
    public float AverageFPS => averageFPS;
    public float MinFPS => minFPS;
    public float MaxFPS => maxFPS;
    public int EntityCount => entityCount;
    public int SystemCount => systemCount;
}
```

### Integration with Graphy

The project integrates with the Graphy - Ultimate Stats Monitor plugin for advanced performance visualization:

```csharp
public class GraphyIntegration : MonoBehaviour
{
    [SerializeField] private bool enableGraphy = true;
    [SerializeField] private GraphyManager.Mode defaultMode = GraphyManager.Mode.FULL;
    
    private GraphyManager graphyManager;
    private PerformanceMonitor performanceMonitor;
    
    private void Awake()
    {
        graphyManager = FindObjectOfType<GraphyManager>();
        performanceMonitor = PerformanceMonitor.Instance;
        
        if (graphyManager != null && performanceMonitor != null)
        {
            // Configure Graphy
            graphyManager.EnableGraphs = enableGraphy;
            graphyManager.Mode = defaultMode;
            
            // Add custom graph for entity count
            var advancedData = graphyManager.GetComponent<GraphyManagerAdvancedData>();
            if (advancedData != null)
            {
                advancedData.AddCustomValue(
                    "Entities", 
                    () => performanceMonitor.EntityCount, 
                    Color.cyan
                );
            }
        }
    }
}
```

## Implementation Guidelines

When implementing new features or modifying existing ones, follow these guidelines:

### Creating New Components

```csharp
// 1. Define the component
[Serializable] // Add this if you want to see the component in the inspector
public struct MyNewComponent : IComponentData
{
    public float Value;
    public int IntValue;
    public float3 Position;
    
    // Use blittable types for better performance
    // Avoid strings, classes, and other managed types
}

// 2. Create an authoring component
public class MyNewComponentAuthoring : MonoBehaviour
{
    public float value;
    public int intValue;
    public Vector3 position;
    
    class Baker : Baker<MyNewComponentAuthoring>
    {
        public override void Bake(MyNewComponentAuthoring authoring)
        {
            var entity = GetEntity(TransformUsageFlags.Dynamic);
            AddComponent(entity, new MyNewComponent
            {
                Value = authoring.value,
                IntValue = authoring.intValue,
                Position = authoring.position
            });
        }
    }
}
```

### Creating New Systems

```csharp
// 1. Define the system
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(AnotherSystem))] // Specify update order if needed
public partial class MyNewSystem : SystemBase
{
    private EntityQuery myQuery;
    
    protected override void OnCreate()
    {
        // Set up queries
        myQuery = GetEntityQuery(
            ComponentType.ReadOnly<MyNewComponent>(),
            ComponentType.ReadWrite<LocalTransform>()
        );
        
        // Require components for the system to update
        RequireForUpdate<MyNewComponent>();
    }
    
    protected override void OnUpdate()
    {
        // Get command buffer if needed
        var ecb = new EntityCommandBuffer(Allocator.TempJob);
        
        // Process entities
        Entities
            .WithAll<MyNewComponent>()
            .ForEach((Entity entity, ref LocalTransform transform, in MyNewComponent myComponent) =>
            {
                // Process entity
                transform.Position += new float3(0, myComponent.Value * Time.DeltaTime, 0);
            })
            .Schedule(); // Schedule as a job
            
        // Play command buffer
        ecb.Playback(EntityManager);
        ecb.Dispose();
    }
}

// 2. Alternative ISystem implementation (more modern approach)
[UpdateInGroup(typeof(SimulationSystemGroup))]
[BurstCompile]
public partial struct MyNewISystem : ISystem
{
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        // Set up queries and requirements
        state.RequireForUpdate<MyNewComponent>();
    }
    
    [BurstCompile]
    public void OnDestroy(ref SystemState state)
    {
        // Clean up resources
    }
    
    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        // Process entities
        new MyNewJob
        {
            DeltaTime = SystemAPI.Time.DeltaTime
        }.Schedule();
    }
    
    [BurstCompile]
    [WithAll(typeof(MyNewComponent))]
    private partial struct MyNewJob : IJobEntity
    {
        public float DeltaTime;
        
        public void Execute(ref LocalTransform transform, in MyNewComponent myComponent)
        {
            transform.Position += new float3(0, myComponent.Value * DeltaTime, 0);
        }
    }
}
```

### Creating New Aspects

Aspects provide a convenient way to access related components on an entity:

```csharp
// Define an aspect
public readonly partial struct MyEntityAspect : IAspect
{
    public readonly Entity Self;
    
    // Read-write access to components
    public readonly RefRW<LocalTransform> Transform;
    public readonly RefRW<MyNewComponent> MyComponent;
    
    // Read-only access to components
    public readonly RefRO<EnemyTag> EnemyTag;
    
    // Helper methods
    public void MoveUp(float amount)
    {
        Transform.ValueRW.Position += new float3(0, amount, 0);
    }
    
    public bool IsActive()
    {
        return MyComponent.ValueRO.Value > 0;
    }
}

// Use the aspect in a system
public partial class MyAspectSystem : SystemBase
{
    protected override void OnUpdate()
    {
        float deltaTime = Time.DeltaTime;
        
        Entities
            .ForEach((MyEntityAspect aspect) =>
            {
                if (aspect.IsActive())
                {
                    aspect.MoveUp(aspect.MyComponent.ValueRO.Value * deltaTime);
                }
            })
            .Schedule();
    }
}
```

## Common Issues and Solutions

### Issue: System Not Updating

**Symptoms:**
- System's OnUpdate method is not being called
- No errors in the console

**Solutions:**
1. Check if the system has the `RequireForUpdate` component and ensure that component exists
2. Verify the system is in the correct update group
3. Check if the system is disabled in the Entity Debugger
4. Ensure the system's query matches at least one entity

```csharp
// Add this to debug system updates
protected override void OnUpdate()
{
    Debug.Log($"System {GetType().Name} is updating");
    
    // Rest of the system...
}
```

### Issue: Cannot Access Camera.main from Burst-Compiled Code

**Symptoms:**
- Error: "Camera.main is not accessible from Burst-compiled code"
- System fails to compile

**Solution:**
1. Get the camera reference outside of Burst-compiled code
2. Pass the camera position or other needed data as a parameter to the job

```csharp
protected override void OnUpdate()
{
    // Get camera data outside of Burst-compiled code
    var camera = Camera.main;
    if (camera == null)
        return;
        
    var cameraPosition = camera.transform.position;
    var cameraForward = camera.transform.forward;
    
    // Pass to job
    Entities
        .ForEach((ref MyComponent component) =>
        {
            // Use cameraPosition and cameraForward here
            float distance = math.distance(component.Position, cameraPosition);
            float dot = math.dot(math.normalize(component.Position - cameraPosition), cameraForward);
            
            // Process based on camera data
        })
        .Schedule();
}
```

### Issue: Managed Types in Burst-Compiled Code

**Symptoms:**
- Error: "Type is not supported by Burst"
- System fails to compile

**Solution:**
1. Replace managed types with blittable types
2. Move code using managed types outside of Burst-compiled methods
3. Use native containers instead of managed collections

```csharp
// Instead of
public class MyManager : MonoBehaviour
{
    public List<string> Names;
}

// Use
public class MyManager : MonoBehaviour
{
    public List<string> Names;
    
    // Add a method to convert to native array
    public NativeArray<int> GetNameLengths(Allocator allocator)
    {
        var result = new NativeArray<int>(Names.Count, allocator);
        for (int i = 0; i < Names.Count; i++)
        {
            result[i] = Names[i].Length;
        }
        return result;
    }
}

// In the system
protected override void OnUpdate()
{
    var manager = MyManager.Instance;
    if (manager == null)
        return;
        
    // Get native array outside of Burst-compiled code
    var nameLengths = manager.GetNameLengths(Allocator.TempJob);
    
    // Pass to job
    var job = new MyJob
    {
        NameLengths = nameLengths
    };
    
    var handle = job.Schedule(Dependency);
    Dependency = handle;
    
    // Dispose after job completes
    Dependency = nameLengths.Dispose(Dependency);
}
```

### Issue: Entity Command Buffer Structural Changes

**Symptoms:**
- Error: "Structural changes are not allowed during job execution"
- System crashes at runtime

**Solution:**
1. Use EntityCommandBuffer for structural changes
2. Create the command buffer from an appropriate system
3. Play back the command buffer after job completion

```csharp
protected override void OnUpdate()
{
    var ecbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
    var ecb = ecbSystem.CreateCommandBuffer().AsParallelWriter();
    
    Entities
        .ForEach((Entity entity, int entityInQueryIndex, in MyComponent component) =>
        {
            if (ShouldDestroy(component))
            {
                // Schedule destruction instead of doing it directly
                ecb.DestroyEntity(entityInQueryIndex, entity);
            }
            
            if (ShouldCreateNew(component))
            {
                // Schedule creation instead of doing it directly
                var newEntity = ecb.Instantiate(entityInQueryIndex, component.Prefab);
                ecb.SetComponent(entityInQueryIndex, newEntity, new LocalTransform
                {
                    Position = component.Position,
                    Rotation = quaternion.identity,
                    Scale = 1
                });
            }
        })
        .ScheduleParallel();
        
    // Register the job with the ECB system
    ecbSystem.AddJobHandleForProducer(Dependency);
}
```
