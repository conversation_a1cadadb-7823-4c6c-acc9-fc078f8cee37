# ECS Technical Documentation - Part 2: Detection and Targeting

## Introduction

This document details the detection and targeting systems used in the project. These systems are responsible for identifying enemies, determining which are in the player's field of view, and selecting the best target to attack.

## Table of Contents

1. [Detection Components](#detection-components)
2. [Detection Systems](#detection-systems)
3. [Targeting Systems](#targeting-systems)
4. [Spatial Partitioning](#spatial-partitioning)
5. [Implementation Guidelines](#implementation-guidelines)

## Detection Components

### Core Detection Components

```csharp
// Detection sensor component
public struct SphereDetectSensorComponent : IComponentData
{
    public bool CanDetect;
    public bool CanLose;
    public float CooldownTime;
    public float3 LastPosition;
    public int EntityIndex;
    public Entity LastDetectedEntity;
    public float3 SensorPosition;
    public float DetectionRadius;
    public float DetectionRange;
    public float DetectionAngle;
    public uint BelongsTo;
    public uint CollidesWith;
    public float CheckInterval;
    public float LastCheckTime;
    public Entity ClosestOutFOVTarget;
    public bool UseKDTree;
    public float TargetSwitchDistanceThreshold;
    public float MinTargetSwitchDistance;
    public float TargetSwitchAngleThreshold;
    public float TargetSwitchScoreThreshold;
}

// Weapon detection settings
public struct WeaponDetectionSettings : IComponentData
{
    public float DetectionRadius;
    public float DetectionRange;
    public float DetectionAngle;
    public float ConeAngle;
    public float ConeRange;
}

// Detection target singleton
public struct DetectionTargetComponent : IComponentData
{
    public Entity CurrentTarget;
    public float3 CurrentPosition;
    public bool HasTarget;
    public bool IsInFOV;
    public bool IsAiming;
    public bool IsDetected;
    public float3 LastKnownPosition;
    public float Score;
}

// Buffer for detected enemies
public struct DetectedEnemyBuffer : IBufferElementData
{
    public Entity Entity;
    public bool IsInFOV;
    public float3 Position;
    public float Score;
}
```

### Detection Tags

```csharp
// Detection tags
public struct DetectedTag : IComponentData { }
public struct InFOVTag : IComponentData { }
public struct CurrentTargetTag : IComponentData { }
public struct UndetectableTag : IComponentData { }
```

## Detection Systems

### OptimizedDetectionSystem

The `OptimizedDetectionSystem` is the core system responsible for detecting enemies within the player's field of view.

Key features:
- Uses spatial partitioning (KD-tree or spatial hash grid) for efficient enemy finding
- Performs field-of-view (FOV) calculations to determine visible enemies
- Adds appropriate tags to detected entities
- Updates the detection target singleton with current target information
- Handles newly spawned entities to prevent premature detection

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class OptimizedDetectionSystem : SystemBase
{
    protected override void OnCreate()
    {
        // Set up queries for player, weapons, enemies, etc.
        playerQuery = GetEntityQuery(new EntityQueryBuilder(Allocator.Temp)
            .WithAll<PlayerTag, SphereDetectSensorComponent, LocalToWorld,
                PlayerHeadTransformComponent>());

        weaponQuery = GetEntityQuery(ComponentType.ReadOnly<WeaponBodyPartComponent>());

        detectedEnemiesQuery = GetEntityQuery(new EntityQueryDesc
        {
            All = new ComponentType[]
            {
                ComponentType.ReadOnly<EnemyTag>(),
                ComponentType.ReadOnly<DetectedTag>(),
                ComponentType.ReadOnly<LocalToWorld>(),
                ComponentType.ReadOnly<EnemyBodyPartsPositionComponent>()
            }
        });

        // Additional queries for tags and spatial partitioning
        inFOVTagQuery = GetEntityQuery(ComponentType.ReadOnly<InFOVTag>());
        currentTargetTagQuery = GetEntityQuery(ComponentType.ReadOnly<CurrentTargetTag>());
        kdTreeQuery = GetEntityQuery(ComponentType.ReadOnly<KDTreeComponent>());
        m_EnemyQuery = GetEntityQuery(
            ComponentType.ReadOnly<EnemyTag>(),
            ComponentType.ReadOnly<LocalToWorld>(),
            ComponentType.Exclude<UndetectableTag>(),
            ComponentType.Exclude<DeadTag>()
        );
    }

    protected override void OnUpdate()
    {
        // Get player and sensor data
        var playerEntity = playerQuery.GetSingletonEntity();
        var sensor = EntityManager.GetComponentData<SphereDetectSensorComponent>(playerEntity);
        var playerTransform = EntityManager.GetComponentData<LocalToWorld>(playerEntity);
        var playerHeadTransform = EntityManager.GetComponentData<PlayerHeadTransformComponent>(playerEntity);
        
        // Check if it's time to update detection
        float currentTime = Time.ElapsedTime;
        if (currentTime - sensor.LastCheckTime < sensor.CheckInterval)
            return;
            
        // Update last check time
        var sensorCopy = sensor;
        sensorCopy.LastCheckTime = currentTime;
        EntityManager.SetComponentData(playerEntity, sensorCopy);
        
        // Use spatial partitioning to find nearby enemies
        if (sensor.UseKDTree && !kdTreeQuery.IsEmpty)
        {
            // Use KD-tree for spatial queries
            // ...
        }
        else
        {
            // Fallback to direct entity iteration
            // ...
        }
        
        // Process detected enemies
        // ...
    }
}
```

### SensorUpdateSystem

The `SensorUpdateSystem` updates the `SphereDetectSensorComponent` based on the current weapon's detection settings.

```csharp
[UpdateAfter(typeof(WeaponDetectionSettingsSystem))]
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class SensorUpdateSystem : SystemBase
{
    protected override void OnUpdate()
    {
        // Update sensor parameters based on weapon settings
        Entities
            .WithAll<PlayerTag>()
            .ForEach((ref SphereDetectSensorComponent sensor, in WeaponDetectionSettings settings) =>
            {
                // Update detection parameters
                sensor.DetectionRadius = settings.DetectionRadius;
                sensor.DetectionRange = settings.DetectionRange;
                sensor.DetectionAngle = settings.DetectionAngle;
            })
            .Schedule();
    }
}
```

## Targeting Systems

### AimingModule

The `AimingModule` selects the best target from detected enemies based on various criteria.

Key features:
- Scores enemies based on distance, angle, and other factors
- Selects the highest-scoring enemy as the current target
- Handles target switching with hysteresis to prevent rapid switching
- Integrates with the TriadCluster system for improved targeting

```csharp
public class AimingModule : MonoBehaviour
{
    // Target selection methods
    public Entity GetBestTarget(NativeArray<Entity> detectedEntities, NativeArray<float3> positions)
    {
        // Score each entity
        // Select the highest-scoring entity
        // Apply hysteresis to prevent rapid switching
        // Return the best target
    }
    
    // Check if any target is behind the player
    public bool IsAnyTargetBehindPlayer(float3 playerPosition, float3 playerForward, float behindAngle)
    {
        // Check if any detected enemy is behind the player
        // Return true if found, false otherwise
    }
}
```

### TriadClusterSystem

The `TriadClusterSystem` groups nearby enemies into clusters for better targeting.

Key features:
- Groups enemies that are close to each other into clusters
- Calculates cluster centroids and sizes
- Scores clusters based on distance, size, and threat level
- Helps prevent targeting isolated enemies when clusters are present

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateBefore(typeof(OptimizedDetectionSystem))]
public partial class TriadClusterSystem : SystemBase
{
    protected override void OnUpdate()
    {
        // Find all detected enemies
        // Group them into clusters based on proximity
        // Calculate cluster properties (centroid, size, etc.)
        // Score clusters based on various factors
        // Update cluster data for use by the targeting system
    }
}
```

## Spatial Partitioning

The project uses two spatial partitioning systems to efficiently find nearby entities:

### KDTreeSystem

The `KDTreeSystem` maintains a KD-tree data structure for efficient spatial queries.

```csharp
[BurstCompile]
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateBefore(typeof(SpatialHashGridSystem))]
public partial struct KDTreeSystem : ISystem
{
    private EntityQuery m_EnemyQuery;
    private EntityQuery m_TreeQuery;
    
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        // Set up queries
        // Create KD-tree entity if it doesn't exist
    }
    
    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        // Get all enemy positions
        // Build or update the KD-tree
        // Store the tree for use by other systems
    }
}
```

### SpatialHashGridSystem

The `SpatialHashGridSystem` provides an alternative spatial partitioning method using a grid-based approach.

```csharp
[BurstCompile]
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial struct SpatialHashGridSystem : ISystem
{
    private EntityQuery m_EnemyQuery;
    private EntityQuery m_GridQuery;
    
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        // Set up queries
        // Create grid entity if it doesn't exist
    }
    
    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        // Get all enemy positions
        // Assign enemies to grid cells
        // Store the grid for use by other systems
    }
}
```

## Implementation Guidelines

When implementing or modifying detection and targeting systems, follow these guidelines:

1. **Performance Considerations**:
   - Use spatial partitioning (KD-tree or spatial hash grid) for efficient enemy finding
   - Limit detection updates using the `CheckInterval` parameter
   - Use Burst compilation and jobs for performance-critical code
   - Avoid allocations in hot paths

2. **Target Selection**:
   - Use a scoring system that considers multiple factors (distance, angle, threat)
   - Implement hysteresis to prevent rapid target switching
   - Consider using the TriadCluster system for improved targeting
   - Handle special cases like enemies behind the player

3. **Detection Parameters**:
   - Store detection parameters in the weapon configuration
   - Update sensor parameters when the weapon changes
   - Allow for upgrades to detection parameters

4. **Integration with Other Systems**:
   - Ensure detection systems update before targeting systems
   - Coordinate with animation systems for proper aiming animations
   - Integrate with weapon systems for accurate firing
