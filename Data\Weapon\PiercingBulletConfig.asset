%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 546374d2430cb1e4aaa47c3441db500a, type: 3}
  m_Name: PiercingBulletConfig
  m_EditorClassIdentifier: 
  bulletName: Piercing Bullet
  bulletType: 5
  bulletPrefab: {fileID: 0}
  baseStats:
    speed: 25
    damageMultiplier: 1.1
    maxDistance: 120
    canPenetrate: 1
    maxPenetrations: 1
    penetrationDamageReduction: 0.3
    explodeOnImpact: 0
    explosionRadius: 0
    explosionDamageMultiplier: 0.7
    impactEffectPrefab: {fileID: 0}
    trailEffectPrefab: {fileID: 0}
  upgradeLevels:
  - levelName: Enhanced Piercing Bullet
    upgradeCost: 200
    stats:
      speed: 28
      damageMultiplier: 1.2
      maxDistance: 130
      canPenetrate: 1
      maxPenetrations: 2
      penetrationDamageReduction: 0.25
      explodeOnImpact: 0
      explosionRadius: 0
      explosionDamageMultiplier: 0.7
      impactEffectPrefab: {fileID: 0}
      trailEffectPrefab: {fileID: 0}
  - levelName: Advanced Piercing Bullet
    upgradeCost: 400
    stats:
      speed: 30
      damageMultiplier: 1.3
      maxDistance: 140
      canPenetrate: 1
      maxPenetrations: 3
      penetrationDamageReduction: 0.2
      explodeOnImpact: 0
      explosionRadius: 0
      explosionDamageMultiplier: 0.7
      impactEffectPrefab: {fileID: 0}
      trailEffectPrefab: {fileID: 0}
