# Target Selection System - Technical Guide

This document provides technical details about the target selection system implementation for developers.

## System Architecture

The target selection system consists of several components:

1. **TriadClusterSettingsComponent**: Defines parameters for the clustering algorithm
2. **DetectionTargetSingleton**: Manages target detection and selection
3. **AimingModule**: Handles target tracking and aiming behavior

## TriadCluster Algorithm

The TriadCluster algorithm groups nearby entities into clusters and selects the best cluster based on multiple factors:

```csharp
// Pseudocode for the TriadCluster algorithm
function CreateClusters(entities, clusteringRadius):
    clusters = []
    
    foreach entity in entities:
        foundCluster = false
        
        foreach cluster in clusters:
            if Distance(entity.position, cluster.centroid) <= clusteringRadius:
                cluster.AddMember(entity)
                foundCluster = true
                break
                
        if not foundCluster:
            newCluster = new Cluster(entity)
            clusters.Add(newCluster)
            
    return clusters
    
function ScoreClusters(clusters, playerPosition):
    foreach cluster in clusters:
        distanceScore = -Distance(cluster.centroid, playerPosition) * ClusterScoreWeight_CentroidDistance
        memberScore = cluster.memberCount * ClusterScoreWeight_MemberCount
        averageScore = cluster.averageEntityScore * ClusterScoreWeight_AverageScore
        
        cluster.score = distanceScore + memberScore + averageScore
        
    return SortByScore(clusters)
    
function SelectTargetFromCluster(cluster, targetSelectionMode):
    switch targetSelectionMode:
        case HighestScore:
            return cluster.GetHighestScoringMember()
        case ClosestToCentroid:
            return cluster.GetClosestToCentroid()
        case ClosestToPlayer:
            return cluster.GetClosestToPlayer()
```

## Target Stability Mechanism

To prevent rapid target switching when enemies are close together, we implemented a stability mechanism:

```csharp
// Pseudocode for target stability
function ShouldSwitchTarget(currentTarget, newTarget, lastSwitchTime):
    // Enforce minimum switch time
    if Time.time - lastSwitchTime < minTargetSwitchTime:
        return false
        
    // Check if this is the same potential target we've been tracking
    if newTarget == potentialTarget:
        // Check if it's been stable long enough
        if Time.time - targetStabilityTimer < targetStabilityThreshold:
            return false
    else:
        // Reset stability timer for new potential target
        potentialTarget = newTarget
        targetStabilityTimer = Time.time
        return false
        
    // Check if the new target is significantly better
    scoreDifference = newTarget.score - currentTarget.score
    if scoreDifference < minScoreDifferenceToSwitch:
        return false
        
    return true
```

## Adaptive Position Smoothing

To make target transitions feel natural, we implemented adaptive position smoothing:

```csharp
// Pseudocode for adaptive position smoothing
function SmoothTargetPosition(currentPosition, targetPosition):
    // Calculate distance to new position
    distanceToNewPosition = Distance(smoothedPosition, targetPosition)
    
    // Adjust smoothing factor based on distance
    // Faster for small changes, slower for large jumps
    adaptiveSmoothingFactor = Lerp(
        targetPositionSmoothingFactor * 2.0f,
        targetPositionSmoothingFactor * 0.5f,
        Clamp01(distanceToNewPosition / 5.0f))
        
    // Apply smoothing with adaptive factor
    smoothedPosition = Lerp(smoothedPosition, targetPosition, 
        Time.deltaTime * adaptiveSmoothingFactor)
        
    return smoothedPosition
```

## Implementation Details

### TriadClusterSettingsComponent

The `TriadClusterSettingsComponent` is a data component that defines the parameters for the clustering algorithm:

```csharp
public struct TriadClusterSettingsComponent : IComponentData
{
    public bool EnableClustering;
    public float ClusteringRadius;
    public float ClusterScoreWeight_CentroidDistance;
    public float ClusterScoreWeight_MemberCount;
    public float ClusterScoreWeight_AverageScore;
    public IntraClusterTargetSelectionMode TargetSelectionMode;
    public int MaxSimultaneousTargets;
    public float ClusterHysteresisThreshold;
}
```

### AimingModule

The `AimingModule` handles target tracking and aiming behavior:

1. It receives target information from the `DetectionTargetSingleton`
2. It applies stability and smoothing algorithms
3. It updates the aim target position
4. It broadcasts events for other systems to respond to

### DetectionTargetSingleton

The `DetectionTargetSingleton` manages target detection and selection:

1. It queries the ECS world for potential targets
2. It applies the TriadCluster algorithm to group and score targets
3. It selects the best target based on the configured parameters
4. It provides target information to the `AimingModule`

## Performance Considerations

1. **Query Optimization**: The system uses efficient ECS queries to find potential targets
2. **Caching**: Target information is cached to avoid redundant calculations
3. **Adaptive Processing**: The system adjusts processing based on the number of entities
4. **Burst Compilation**: Critical parts of the algorithm are Burst-compiled for performance

## Extending the System

### Adding New Target Selection Modes

To add a new target selection mode:

1. Add a new value to the `IntraClusterTargetSelectionMode` enum
2. Implement the selection logic in the `SelectTargetFromCluster` method
3. Update the documentation to describe the new mode

### Adding New Scoring Factors

To add a new factor for scoring targets:

1. Add a new weight parameter to the `TriadClusterSettingsComponent`
2. Implement the scoring logic in the `ScoreClusters` method
3. Update the documentation to describe the new factor

## Debugging

The system includes several debugging features:

1. **Gizmos**: The `AimingModule` draws gizmos to visualize the detection cone
2. **Debug Logs**: Important target switching events are logged
3. **Inspector Fields**: All parameters can be adjusted in the inspector

To enable detailed debugging:

1. Enable the "Debug Mode" checkbox in the `AimingModule` inspector
2. Use the Unity Profiler to monitor performance
3. Check the console for target switching logs
