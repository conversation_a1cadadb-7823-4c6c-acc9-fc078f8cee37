# ECS Technical Documentation

This document provides a comprehensive overview of the Entity Component System (ECS) architecture used in the project. It serves as a reference for developers implementing new features or modifying existing functionality.

## Table of Contents

1. [ECS Architecture Overview](#ecs-architecture-overview)
2. [Core Components](#core-components)
3. [Main Systems](#main-systems)
4. [Subsystems](#subsystems)
   - [Detection and Targeting](#detection-and-targeting)
   - [Weapon and Shooting](#weapon-and-shooting)
   - [Health and Damage](#health-and-damage)
   - [Animation](#animation)
   - [Enemy Spawning and AI](#enemy-spawning-and-ai)
   - [Spatial Partitioning](#spatial-partitioning)
5. [Performance Optimization](#performance-optimization)
6. [Integration with External Systems](#integration-with-external-systems)
7. [Debugging and Monitoring](#debugging-and-monitoring)
8. [Implementation Guidelines](#implementation-guidelines)

## ECS Architecture Overview

The project uses Unity's Data-Oriented Technology Stack (DOTS) with Entity Component System (ECS) to achieve high performance with large numbers of entities. The architecture follows these principles:

- **Components**: Pure data containers with no behavior
- **Systems**: Logic that operates on components
- **Entities**: Lightweight identifiers that components are attached to
- **Aspects**: Convenient views of related components on an entity
- **Jobs**: Parallelized work that operates on components
- **Burst Compilation**: Ahead-of-time compilation for high-performance code

The project is organized into several key subsystems that handle different aspects of gameplay, such as detection, targeting, shooting, health, animation, and enemy AI.

## Core Components

### Player Components

```csharp
// Player identification
public struct PlayerTag : IComponentData { }

// Player transform data
public struct PlayerTransform : IComponentData
{
    public float3 Position;
    public quaternion Rotation;
}

// Player head transform for aiming
public struct PlayerHeadTransformComponent : IComponentData
{
    public float3 Position;
    public float3 Forward;
    public quaternion Rotation;
}

// Player bones for animation
public struct PlayerBonesTransformComponent : IComponentData
{
    public UnityObjectRef<Transform> HeadTransform;
}

// Player movement data
public struct MovementData : IComponentData
{
    // Movement properties
}
```

### Enemy Components

```csharp
// Enemy identification
public struct EnemyTag : IComponentData { }

// Enemy movement speed
public struct EnemyMovementSpeed : IComponentData
{
    public float Value;
}

// Enemy body parts positions for targeting
public struct EnemyBodyPartsPositionComponent : IComponentData
{
    public float3 HeadPosition;
    public float3 ChestPosition;
    public float3 LeftArmPosition;
    public float3 RightArmPosition;
    public float3 Hip;
    public float3 LeftLegPosition;
    public float3 RightLegPosition;
}

// Spawn tracking
public struct SpawnComponent : IComponentData
{
    public float SpawnTime;    // Time when the entity was spawned
}
```

### Detection Components

```csharp
// Detection sensor
public struct SphereDetectSensorComponent : IComponentData
{
    public float DetectionRange;
    public float DetectionAngle;
    public float CheckInterval;
    public float TargetSwitchDistanceThreshold;
    public float MinTargetSwitchDistance;
    public float TargetSwitchAngleThreshold;
    public float TargetSwitchScoreThreshold;
    public bool UseKDTree;
    public PhysicsCategoryTags BelongsTo;
    public PhysicsCategoryTags CollidesWith;
}

// Detection tags
public struct DetectedTag : IComponentData { }
public struct InFOVTag : IComponentData { }
public struct CurrentTargetTag : IComponentData { }
public struct UndetectableTag : IComponentData { }

// Detection target singleton
public struct DetectionTargetComponent : IComponentData
{
    public Entity CurrentTarget;
    public float3 CurrentPosition;
    public float3 LastKnownPosition;
    public bool HasTarget;
    public bool IsInFOV;
    public bool IsDetected;
    public float Score;
}

// Buffer for detected enemies
public struct DetectedEnemyBuffer : IBufferElementData
{
    public Entity Entity;
    public float3 Position;
    public bool IsInFOV;
    public float Score;
}
```

### Weapon Components

```csharp
// Shooting properties
public struct ShootingComponent : IComponentData
{
    public float FireRate;
    public float Accuracy;
    public float MaxRange;
    public float DamageAmount;
    public float LastShotTime;
    public float SpreadAngle;
    public float RecoilAmount;
    public float RecoilRecovery;
    public float CurrentRecoil;
    public PhysicsCategoryTags BelongsTo;
    public PhysicsCategoryTags CollideWith;
    public BulletType CurrentBulletType;
}

// Weapon state
public struct WeaponStateComponent : IComponentData
{
    public bool IsReloading;
    public int CurrentAmmo;
    public int MaxAmmo;
    public float ReloadTime;
    public float ReloadStartTime;
    public bool IsFiring;
    public float3 AimPointer;
    public WeaponSubModuleState WeaponType;
    public int WeaponLevel;
}

// Bullet properties
public struct BulletComponent : IComponentData
{
    public PhysicsCategoryTags BelongsTo;
    public PhysicsCategoryTags CollideWith;
    public float3 InitialPosition;
    public float3 Direction;
    public float Speed;
    public float Damage;
    public float MaxDistance;
    public float TraveledDistance;
    public Entity Shooter;
    public bool Active;
    public BulletType BulletType;
    
    // Penetration properties
    public bool CanPenetrate;
    public int MaxPenetrations;
    public int PenetrationCount;
    public float PenetrationDamageReduction;
    
    // Explosion properties
    public bool ExplodeOnImpact;
    public float ExplosionRadius;
    public float ExplosionDamageMultiplier;
    
    // Visual effects
    public Entity ImpactEffectPrefab;
    public Entity TrailEffectPrefab;
}
```

### Health Components

```csharp
// Health data
public struct HealthComponent : IComponentData
{
    public float MaxHealth;
    public float CurrentHealth;
}

// Damage event
public struct DamageEvent : IComponentData
{
    public float DamageAmount;
    public Entity Source;
}

// Death tag
public struct DeadTag : IComponentData { }

// Death animation and dissolve effect
public struct DeathTimerComponent : IComponentData
{
    public float ElapsedTime;
    public float DissolveStartTime;
    public float DissolveDuration;
    public bool DissolveStarted;
}

// Health bar references
public struct HealthBarUIReference : IComponentData
{
    public Entity UIEntity;
}

public struct HealthBarUIOffset : IComponentData
{
    public float3 Offset;
}

// Always show health bar tag
public struct AlwaysShowHealthBarTag : IComponentData { }
```

### Animation Components

```csharp
// Character movement state for animation
public struct CharacterMovementState : IComponentData
{
    public float Speed;
    public bool IsMoving;
    public bool IsAttacking;
    public float3 Direction;
    public int CurrentAnimationID;
}

// Character animator reference
public struct CharacterAnimatorReference : IComponentData
{
    public Entity AnimatorEntity;
}

// Death animation started tag
public struct DeathAnimationStartedTag : IComponentData { }

// Hit animation tag
public struct HitTag : IComponentData { }
```

## Main Systems

### System Groups

The project organizes systems into logical groups for better update order control:

```csharp
// Health systems group
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class HealthSystemsGroup : ComponentSystemGroup
{
    protected override void OnCreate()
    {
        base.OnCreate();
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<HealthBarSystemsGroup>());
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<DamageSystem>());
    }
}

// Health bar systems group
[UpdateInGroup(typeof(HealthSystemsGroup))]
public partial class HealthBarSystemsGroup : ComponentSystemGroup
{
    protected override void OnCreate()
    {
        base.OnCreate();
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<HealthBarSpawnSystem>());
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<HealthBarUpdateSystem>());
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<HealthBarCleanupSystem>());
    }
}
```

### Initialization Systems

```csharp
// Spawn tracking system
[UpdateInGroup(typeof(InitializationSystemGroup))]
public partial class SpawnTrackingSystem : SystemBase
{
    // Adds SpawnComponent to newly spawned entities
    // Tracks when entities were spawned to prevent issues with health bars
}
```

## Subsystems

### Detection and Targeting

The detection and targeting subsystem is responsible for identifying enemies within the player's field of view and selecting targets for the player to attack.

#### Key Systems:

1. **OptimizedDetectionSystem**: Core system for detecting enemies within the player's field of view
   - Uses spatial partitioning (KDTree) for efficient enemy detection
   - Calculates scores for enemies based on distance and angle
   - Manages target selection with hysteresis to prevent rapid target switching
   - Updates the DetectionTargetComponent singleton with current target information

2. **EnemyTriggerDetectionSystem**: Handles trigger-based detection events
   - Processes physics trigger events for enemy detection
   - Complements the OptimizedDetectionSystem for close-range detection

#### Implementation Details:

- The system uses a combination of physics raycasts and spatial partitioning for efficient detection
- Target selection uses a scoring system that considers:
  - Distance to target
  - Angle from player's forward direction
  - Current target status (with hysteresis to prevent rapid switching)
- Detection results are stored in a buffer on a singleton entity for easy access by other systems

#### Performance Considerations:

- KDTree spatial partitioning is used for efficient enemy queries
- The system uses Burst-compiled jobs for parallel processing
- Detection checks are throttled by a configurable interval to reduce CPU usage

### Weapon and Shooting

The weapon and shooting subsystem handles weapon state, firing logic, and bullet behavior.

#### Key Systems:

1. **OptimizedShootingSystem**: Manages weapon firing and bullet creation
   - Handles weapon state (firing, reloading)
   - Creates and configures bullets based on weapon properties
   - Applies weapon upgrades from the WeaponUpgradeManager

2. **BulletSystem**: Updates bullet positions and handles collisions
   - Moves bullets along their trajectory
   - Detects collisions with enemies and environment
   - Applies damage to hit entities
   - Handles special bullet effects (penetration, explosion)

#### Implementation Details:

- Weapons are configured using ScriptableObjects (WeaponConfig, BulletConfig)
- The WeaponUpgradeManager handles weapon progression and stat improvements
- Different bullet types (Normal, Fire, Ice, etc.) have different behaviors and effects
- Bullets use Unity Physics for collision detection

#### Performance Considerations:

- Bullet updates are handled in Burst-compiled jobs
- Bullet pooling is used to reduce garbage collection
- Maximum bullet count is enforced to prevent performance issues

### Health and Damage

The health and damage subsystem manages entity health, damage processing, and health bar UI.

#### Key Systems:

1. **DamageSystem**: Processes damage events and updates health
   - Applies damage to entities with HealthComponent
   - Triggers death when health reaches zero
   - Adds DeadTag and related components for death processing

2. **HealthBarSpawnSystem**: Creates and manages health bar UI elements
   - Spawns health bar UI for detected enemies
   - Positions health bars above entities
   - Updates health bar fill amount based on current health

3. **HealthBarCleanupSystem**: Handles health bar cleanup and hiding
   - Removes health bars from dead entities
   - Hides health bars for entities that are no longer detected
   - Manages health bar pooling and reuse

4. **AllEnemiesHealthBarSystem**: Optional system for showing all enemy health bars
   - Shows health bars for all enemies regardless of detection status
   - Only active when alwaysShowHealthBars is enabled in configuration

#### Implementation Details:

- Health bars are implemented as world-space UI elements
- Health bar pooling is used to reduce instantiation overhead
- Health bars can be configured to always show or only show for detected enemies
- Death processing includes animation, dissolve effect, and entity cleanup

#### Performance Considerations:

- Health bar pooling with strict reuse rules to prevent excessive instantiation
- Health bar updates are throttled to reduce UI overhead
- Health bar visibility is tied to detection status to reduce active UI elements

### Animation

The animation subsystem handles character animations based on movement and state.

#### Key Systems:

1. **EnemyAnimationSystem**: Manages enemy animations
   - Updates animation state based on movement and actions
   - Handles transitions between animation states
   - Integrates with GPU ECS Animator for efficient animation

2. **DeathAnimationSystem**: Handles death animations
   - Plays death animation when an entity is marked as dead
   - Adds DeathAnimationStartedTag to track animation progress
   - Prepares entity for dissolve effect after animation

3. **HitAnimationSystem**: Manages hit reaction animations
   - Plays hit animation when an entity takes damage
   - Handles animation blending and transitions

#### Implementation Details:

- Animations are defined using the EnemyAnimationIDs enum:
  - Idle = 0
  - Walk = 1
  - Attack = 2
  - Hit = 3
  - Dead = 4
- The system integrates with GPU ECS Animator for efficient GPU-based animation
- Animation transitions are handled based on movement state and actions

#### Performance Considerations:

- GPU-based animation for efficient handling of many animated characters
- Animation updates are Burst-compiled for performance
- Dead entities are handled separately to avoid structural changes in jobs

### Enemy Spawning and AI

The enemy spawning and AI subsystem handles enemy creation, movement, and behavior.

#### Key Systems:

1. **CharacterSpawnerSystem**: Manages enemy spawning
   - Creates enemies based on spawn configuration
   - Controls spawn rate and maximum enemy count
   - Integrates with Crowds navigation for pathfinding

2. **EnemyMovementSystem**: Controls enemy movement
   - Updates enemy destinations to follow the player
   - Integrates with Crowds navigation for pathfinding
   - Handles movement speed and direction

3. **EnemyAttackTriggerSystem**: Manages enemy attack behavior
   - Randomly triggers attack animations for testing
   - Controls attack timing and frequency

#### Implementation Details:

- Enemies are spawned using a prefab-based approach
- Enemy movement uses the Crowds navigation system for efficient pathfinding
- Enemy behavior is controlled by simple state machines
- Spawn tracking helps manage entity lifecycle and UI elements

#### Performance Considerations:

- Spawn count is limited to maintain performance
- Crowds navigation is optimized for large numbers of agents
- Enemy updates are Burst-compiled for performance
- Spatial partitioning is used for efficient enemy queries

### Spatial Partitioning

The spatial partitioning subsystem provides efficient spatial queries for entity detection and interaction.

#### Key Systems:

1. **KDTreeSystem**: Manages a KD-tree for spatial queries
   - Builds and updates a KD-tree with enemy positions
   - Provides efficient radius and nearest-neighbor queries
   - Integrates with the detection system for efficient enemy finding

2. **SpatialHashGridSystem**: Alternative spatial partitioning system
   - Maintains a grid-based spatial hash for quick entity lookup
   - Provides efficient spatial queries for nearby entities
   - Complements the KD-tree for different query types

#### Implementation Details:

- The KD-tree is implemented using the GimmeDOTSGeometry library
- The spatial hash grid uses a fixed cell size for efficient lookups
- Both systems update automatically as entities move
- Query results are cached to reduce redundant calculations

#### Performance Considerations:

- KD-tree is optimized periodically to maintain query efficiency
- Spatial hash grid uses fixed-size cells to balance memory and performance
- Both systems use Burst-compiled jobs for parallel processing
- Query results are reused across systems to reduce redundant calculations

## Performance Optimization

### DOTS and Burst Compilation

The project extensively uses Unity's Data-Oriented Technology Stack (DOTS) and Burst compilation for high performance:

1. **Entity Component System (ECS)**:
   - Components are pure data, enabling efficient memory layout
   - Systems operate on components in a data-oriented manner
   - Entities are lightweight identifiers, reducing memory overhead

2. **Burst Compilation**:
   - Critical systems are Burst-compiled for high performance
   - Jobs are used for parallel processing of entities
   - Native containers are used to avoid garbage collection

3. **Job System**:
   - Parallel processing of entities using the C# Job System
   - Dependency management for efficient job scheduling
   - Batch processing for better cache utilization

### Spatial Partitioning

The project uses efficient spatial partitioning techniques to reduce the cost of spatial queries:

1. **KD-Tree**:
   - Efficient logarithmic-time spatial queries
   - Used for enemy detection and targeting
   - Periodically optimized to maintain performance

2. **Spatial Hash Grid**:
   - Constant-time spatial queries for fixed-size cells
   - Used for collision detection and proximity queries
   - Complements the KD-tree for different query types

### GPU-Based Animation

The project uses GPU-based animation for efficient character animation:

1. **GPU ECS Animator**:
   - Animations are processed on the GPU
   - Supports large numbers of animated characters
   - Integrates with ECS for efficient data flow

2. **Animation Instancing**:
   - Similar characters share animation data
   - Reduces memory usage and CPU overhead
   - Enables efficient rendering of many characters

### Entity Pooling and Reuse

The project uses pooling and reuse strategies to reduce instantiation overhead:

1. **Bullet Pooling**:
   - Bullets are reused rather than destroyed
   - Reduces garbage collection pressure
   - Enables efficient bullet-heavy gameplay

2. **Health Bar Pooling**:
   - Health bars are pooled and reused
   - Strict reuse rules prevent UI flickering
   - Reduces instantiation overhead for UI elements

3. **Enemy Spawning**:
   - Enemies are spawned gradually to distribute load
   - Maximum enemy count is enforced to maintain performance
   - Spawn tracking helps manage entity lifecycle

### Rendering Optimizations

The project includes rendering optimizations for efficient visualization:

1. **GPU Instancing**:
   - Similar meshes are rendered in batches
   - Reduces draw calls and CPU overhead
   - Enables efficient rendering of many characters

2. **Level of Detail (LOD)**:
   - Characters use LOD for distance-based detail reduction
   - Reduces polygon count for distant characters
   - Improves rendering performance with many characters

3. **Culling**:
   - Efficient culling of off-screen entities
   - Reduces rendering overhead
   - Integrates with ECS for data-oriented culling

## Integration with External Systems

### GPU ECS Animator

The project integrates with the GPU ECS Animator package for efficient character animation:

1. **Setup**:
   - Characters use GpuEcsAnimatorBehaviour for GPU-based animation
   - Animations are baked using the GPU ECS Animation Baker
   - Animation states are defined using enums (e.g., EnemyAnimationIDs)

2. **Usage**:
   - The EnemyAnimationSystem updates animation states based on movement
   - Animation transitions are handled automatically
   - Special animations (death, hit) are triggered by specific systems

3. **Performance**:
   - Animations are processed on the GPU for efficiency
   - Many characters can be animated with minimal CPU overhead
   - Animation data is shared between similar characters

### Crowds Navigation

The project integrates with the Agents Navigation - Crowds package for efficient pathfinding:

1. **Setup**:
   - Characters use AgentAuthoring and AgentCrowdPathAuthoring components
   - Navigation is configured using CrowdGroupAuthoring
   - Spawn areas are defined using SpawnerAuthoring

2. **Usage**:
   - The EnemyMovementSystem updates agent destinations
   - Pathfinding is handled automatically by the Crowds system
   - Agents avoid obstacles and other agents automatically

3. **Performance**:
   - Crowds navigation is optimized for large numbers of agents
   - Pathfinding is computed efficiently on the CPU or GPU
   - Agent avoidance is handled automatically

### Graphy - Ultimate Stats Monitor

The project integrates with the Graphy package for performance monitoring:

1. **Setup**:
   - The Graphy prefab is included in the scene
   - Custom performance monitors can be added to track specific metrics
   - Configuration is done through the GraphyManager component

2. **Usage**:
   - FPS, memory, and audio metrics are displayed in real-time
   - Performance issues can be identified and addressed
   - Custom metrics can be added for specific monitoring needs

3. **Features**:
   - Real-time FPS monitoring with min/max/average
   - Memory usage tracking (allocated, reserved, mono)
   - Audio level visualization
   - Advanced system information

## Debugging and Monitoring

### DebugLogManager

The project includes a custom DebugLogManager for efficient and categorized logging:

1. **Setup**:
   - The DebugLogManager is a singleton accessible throughout the project
   - Log categories are defined using the LogType enum
   - Log filtering is configured through the Unity inspector

2. **Usage**:
   ```csharp
   DebugLogManager.Instance.Log("Message", DebugLogManager.LogType.EnemyAnimation);
   ```

3. **Features**:
   - Category-based filtering of log messages
   - Runtime toggling of log categories
   - Conditional compilation for release builds

### Performance Monitoring

The project includes tools for performance monitoring and optimization:

1. **Graphy Integration**:
   - Real-time FPS, memory, and audio monitoring
   - Performance metrics visualization
   - System information display

2. **Custom Performance Metrics**:
   - Entity count tracking
   - System update time measurement
   - Memory allocation monitoring

3. **Profiling Tools**:
   - Integration with Unity Profiler
   - Custom profiling markers for key systems
   - Performance regression testing

## Implementation Guidelines

### Adding New Components

When adding new components to the project, follow these guidelines:

1. **Keep components small and focused**:
   - Components should contain only related data
   - Split large components into smaller, more focused ones
   - Use buffer components for variable-length data

2. **Use appropriate component types**:
   - IComponentData for most components
   - IBufferElementData for variable-length data
   - ISharedComponentData for data shared by many entities

3. **Consider memory layout**:
   - Group related fields together
   - Use appropriate data types (float3 instead of Vector3)
   - Consider alignment for efficient memory access

Example:
```csharp
// Good component design
public struct EnemyStats : IComponentData
{
    public float Health;
    public float Speed;
    public float AttackDamage;
    public float AttackRange;
}

// Buffer for variable-length data
public struct AttackTarget : IBufferElementData
{
    public Entity Target;
    public float Priority;
}
```

### Adding New Systems

When adding new systems to the project, follow these guidelines:

1. **Use appropriate system types**:
   - SystemBase for most systems
   - ISystem for high-performance Burst-compiled systems
   - ComponentSystemGroup for organizing related systems

2. **Consider update order**:
   - Use UpdateBefore/UpdateAfter attributes
   - Add systems to appropriate system groups
   - Consider dependencies between systems

3. **Optimize for performance**:
   - Use Burst compilation for performance-critical systems
   - Use jobs for parallel processing
   - Minimize managed allocations

Example:
```csharp
// High-performance system
[BurstCompile]
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(DetectionSystem))]
public partial struct TargetingSystem : ISystem
{
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        // Initialization
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        // Update logic using jobs
    }
}
```

### Using Jobs and Burst

When using jobs and Burst compilation, follow these guidelines:

1. **Make jobs Burst-compatible**:
   - Use unmanaged types only
   - Avoid managed allocations
   - Use native containers for data

2. **Optimize job scheduling**:
   - Use ScheduleParallel for parallel processing
   - Manage dependencies correctly
   - Consider job batching for better performance

3. **Handle job safety**:
   - Dispose native containers after use
   - Use safety handles in development builds
   - Test thoroughly for race conditions

Example:
```csharp
[BurstCompile]
private partial struct ProcessEnemiesJob : IJobEntity
{
    [ReadOnly] public float DeltaTime;
    [ReadOnly] public float3 PlayerPosition;
    public EntityCommandBuffer.ParallelWriter ECB;

    void Execute(Entity entity, [EntityIndexInQuery] int sortKey,
                ref EnemyMovementComponent movement,
                in LocalTransform transform)
    {
        // Job logic here
    }
}
```

### Integrating with Mono Behavior Systems

When integrating ECS with MonoBehaviour systems, follow these guidelines:

1. **Use hybrid ECS approach**:
   - Keep performance-critical code in ECS
   - Use MonoBehaviours for integration with Unity systems
   - Consider using ConvertToEntity for automatic conversion

2. **Share data efficiently**:
   - Use ComponentDataFromEntity for ECS to access component data
   - Use EntityManager.GetComponentData for MonoBehaviours to access ECS data
   - Consider using events for communication

3. **Handle lifecycle differences**:
   - ECS systems update in a different order than MonoBehaviours
   - Consider using system groups to control update order
   - Be careful with object destruction and entity deletion

Example:
```csharp
// MonoBehaviour that interacts with ECS
public class PlayerController : MonoBehaviour
{
    private Entity _playerEntity;
    private EntityManager _entityManager;

    private void Start()
    {
        _entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;
        _playerEntity = GetComponent<GameObjectEntity>().Entity;
    }

    private void Update()
    {
        // Get input and update ECS component
        var input = new InputComponent
        {
            Horizontal = Input.GetAxis("Horizontal"),
            Vertical = Input.GetAxis("Vertical"),
            Fire = Input.GetButton("Fire1")
        };

        _entityManager.SetComponentData(_playerEntity, input);
    }
}
```

### Performance Best Practices

When optimizing for performance, follow these guidelines:

1. **Use Burst compilation**:
   - Apply [BurstCompile] attribute to jobs and ISystem implementations
   - Avoid managed types in Burst-compiled code
   - Use native containers for data

2. **Optimize memory access**:
   - Group related components together
   - Use archetype queries efficiently
   - Consider component layout for cache efficiency

3. **Reduce garbage collection**:
   - Use object pooling for frequently created objects
   - Avoid allocations in hot paths
   - Use native containers instead of managed collections

4. **Profile and measure**:
   - Use Unity Profiler to identify bottlenecks
   - Measure performance before and after optimizations
   - Focus on optimizing the most critical systems first

Example:
```csharp
// Efficient entity iteration
Entities
    .WithAll<EnemyTag>()
    .WithNone<DeadTag>()
    .WithBurst()
    .ForEach((ref LocalTransform transform, in EnemyMovementSpeed speed) =>
    {
        // Process entities efficiently
    })
    .ScheduleParallel();
```
