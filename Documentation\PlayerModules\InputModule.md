# Input Module Documentation

## Overview

The `InputModule` is responsible for processing player input from various input devices and distributing it to other modules. It acts as the first step in the player control pipeline, converting raw input into game-relevant values.

## Features

- Processes input from Rewired input system
- Handles different input contexts (normal movement, aiming)
- Converts input to camera-relative directions
- Provides smooth input transitions
- Broadcasts input values to other modules

## Implementation

The `InputModule` implements the `IInputModule` interface and the `IHasOutputValue<CharacterParameter>` interface for output distribution.

```csharp
public class InputModule : SerializedMonoBehaviour, IInputModule, IHasOutputValue<CharacterParameter>,
    INeedState<MovementSubState>
{
    [field: SerializeField] public int ControllerIndex { get; private set; }
    [field: SerializeField] public List<MainState> MainState { get; private set; }
    [field: SerializeField] public PlaceHolder SubState { get; private set; }
    
    [field: SerializeField]
    public Dictionary<CharacterParameter, List<Action<object>>> OutputValue { get; private set; }
    
    // Input processing methods
    // ...
}
```

## Input Processing

The module processes input in several steps:

1. **Raw Input Collection**: Collects raw input from the Rewired system
2. **Camera-Relative Conversion**: Converts input to camera-relative directions
3. **Context-Specific Processing**: Applies different processing based on current state (aiming, normal movement)
4. **Smoothing**: Applies smoothing for more natural transitions
5. **Distribution**: Distributes processed input to other modules

### Camera-Relative Input

```csharp
// Get camera forward and right directions
Vector3 cameraForward = Camera.main.transform.forward;
Vector3 cameraRight = Camera.main.transform.right;
cameraForward.y = 0; // Keep the direction horizontal
cameraRight.y = 0; // Keep the direction horizontal
cameraForward.Normalize();
cameraRight.Normalize();

// Determine input direction relative to camera
Vector3 inputDirection = (rawHorizontalInput * cameraRight + rawVerticalInput * cameraForward).normalized;
```

### Input Smoothing

```csharp
// Apply less aggressive smoothing for normal movement
float normalSmoothTime = 0.05f; // Lower value = faster response
horizontalInput = Mathf.Lerp(prevHorizontal, horizontalInput, Time.deltaTime / normalSmoothTime);
verticalInput = Mathf.Lerp(prevVertical, verticalInput, Time.deltaTime / normalSmoothTime);
```

## Input Distribution

The module distributes input to other modules through the `OutputValue` dictionary:

```csharp
OutputValue[CharacterParameter.Horizontal].ForEach(x => x.Invoke(horizontalInput));
OutputValue[CharacterParameter.Vertical].ForEach(x => x.Invoke(verticalInput));
```

## Integration with Other Modules

### Movement Module

The `InputModule` provides horizontal and vertical input values to the `MovementModule`, which uses them to move the player character.

### Aiming Module

The `InputModule` provides aiming input to the `AimingModule`, which uses it to control the player's aim direction.

## Configuration

The `InputModule` can be configured through the Unity Inspector:

- **Input Angle Multiplier**: Controls the sensitivity of input angle calculations
- **Smoothing Parameters**: Controls the smoothness of input transitions
- **Input Mapping**: Maps input actions to specific functions

## Best Practices

1. **Centralize Input Processing**: All input processing should happen in the `InputModule`
2. **Use Event-Based Communication**: Avoid direct references to other modules
3. **Apply Appropriate Smoothing**: Different contexts may require different smoothing parameters
4. **Handle Input Context Changes**: Ensure smooth transitions between different input contexts (e.g., normal movement to aiming)

## Common Issues and Solutions

### Input Feels Delayed

- Reduce smoothing time values
- Check for frame rate issues
- Verify input system configuration

### Input Direction Is Incorrect

- Check camera-relative conversion
- Verify input axis mapping
- Ensure proper normalization of direction vectors

### Input Not Reaching Other Modules

- Verify `OutputValue` dictionary setup
- Check event subscriptions
- Ensure modules are properly initialized
