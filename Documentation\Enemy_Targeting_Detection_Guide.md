# Enemy Targeting and Detection System Guide

This document provides detailed information about the enemy targeting and detection systems in the project, focusing on optimizing performance and addressing issues with target selection and stability.

## Table of Contents

1. [Overview](#overview)
2. [Detection System Architecture](#detection-system-architecture)
3. [Target Selection Algorithm](#target-selection-algorithm)
4. [Triad Cluster System](#triad-cluster-system)
5. [Performance Optimization](#performance-optimization)
6. [Common Issues and Solutions](#common-issues-and-solutions)
7. [Implementation Examples](#implementation-examples)
8. [Troubleshooting](#troubleshooting)

## Overview

The enemy targeting and detection system is responsible for:

1. **Detecting enemies** within the player's field of view and range
2. **Selecting targets** based on distance, angle, and other factors
3. **Maintaining target stability** to prevent rapid switching
4. **Providing target information** to other systems (weapons, UI, etc.)

The system uses a combination of spatial partitioning, physics raycasts, and scoring algorithms to efficiently detect and select targets.

## Detection System Architecture

### Core Components

```csharp
// Detection sensor
public struct SphereDetectSensorComponent : IComponentData
{
    public float DetectionRange;
    public float DetectionAngle;
    public float CheckInterval;
    public float TargetSwitchDistanceThreshold;
    public float MinTargetSwitchDistance;
    public float TargetSwitchAngleThreshold;
    public float TargetSwitchScoreThreshold;
    public bool UseKDTree;
    public PhysicsCategoryTags BelongsTo;
    public PhysicsCategoryTags CollidesWith;
}

// Detection tags
public struct DetectedTag : IComponentData { }
public struct InFOVTag : IComponentData { }
public struct CurrentTargetTag : IComponentData { }
public struct UndetectableTag : IComponentData { }

// Detection target singleton
public struct DetectionTargetComponent : IComponentData
{
    public Entity CurrentTarget;
    public float3 CurrentPosition;
    public float3 LastKnownPosition;
    public bool HasTarget;
    public bool IsInFOV;
    public bool IsDetected;
    public float Score;
}

// Buffer for detected enemies
public struct DetectedEnemyBuffer : IBufferElementData
{
    public Entity Entity;
    public float3 Position;
    public bool IsInFOV;
    public float Score;
}

// Detected enemy data (for internal use)
public struct DetectedEnemyData
{
    public Entity Entity;
    public float3 Position;
    public float Distance;
    public float Angle;
    public bool IsInFOV;
    public float Score;
}
```

### Key Systems

#### OptimizedDetectionSystem

The `OptimizedDetectionSystem` is the core system responsible for detecting enemies and selecting targets:

```csharp
public partial class OptimizedDetectionSystem : SystemBase
{
    private double lastSwitchTime;
    private EntityQuery playerQuery;
    private EntityQuery weaponQuery;
    private EntityQuery detectedEnemiesQuery;
    private EntityQuery inFOVTagQuery;
    private EntityQuery currentTargetTagQuery;
    private EntityQuery detectionTargetQuery;
    private EntityQuery kdTreeQuery;
    private EntityQuery m_EnemyQuery;
    
    protected override void OnCreate()
    {
        // Initialize queries
        playerQuery = GetEntityQuery(new EntityQueryBuilder(Allocator.Temp)
            .WithAll<PlayerTag, SphereDetectSensorComponent, LocalToWorld, PlayerHeadTransformComponent>());
        
        weaponQuery = GetEntityQuery(ComponentType.ReadOnly<WeaponBodyPartComponent>());
        
        detectedEnemiesQuery = GetEntityQuery(new EntityQueryDesc
        {
            All = new ComponentType[]
            {
                ComponentType.ReadOnly<EnemyTag>(),
                ComponentType.ReadOnly<DetectedTag>(),
                ComponentType.ReadOnly<LocalToWorld>(),
                ComponentType.ReadOnly<EnemyBodyPartsPositionComponent>()
            }
        });
        
        inFOVTagQuery = GetEntityQuery(ComponentType.ReadOnly<InFOVTag>());
        currentTargetTagQuery = GetEntityQuery(ComponentType.ReadOnly<CurrentTargetTag>());
        kdTreeQuery = GetEntityQuery(ComponentType.ReadOnly<KDTreeComponent>());
        
        m_EnemyQuery = GetEntityQuery(
            ComponentType.ReadOnly<EnemyTag>(),
            ComponentType.ReadOnly<LocalToWorld>(),
            ComponentType.Exclude<UndetectableTag>(),
            ComponentType.Exclude<DeadTag>()
        );
        
        // Create singleton entity for detection target
        var entity = EntityManager.CreateEntity();
        EntityManager.AddComponentData(entity, new DetectionTargetComponent());
        EntityManager.AddBuffer<DetectedEnemyBuffer>(entity);
        
        detectionTargetQuery = GetEntityQuery(ComponentType.ReadWrite<DetectionTargetComponent>());
    }
    
    protected override void OnUpdate()
    {
        // Implementation details in the following sections
    }
}
```

#### Detection Process Flow

The detection process follows these steps:

1. **Spatial Query**: Use KD-Tree or physics raycasts to find potential targets
2. **Filtering**: Filter out dead, undetectable, or otherwise invalid entities
3. **Scoring**: Calculate scores for each potential target based on distance, angle, etc.
4. **Selection**: Select the best target based on scores and hysteresis rules
5. **Tagging**: Add appropriate tags to entities (DetectedTag, InFOVTag, CurrentTargetTag)
6. **Update Singleton**: Update the DetectionTargetComponent singleton with current target info

## Target Selection Algorithm

The target selection algorithm uses a scoring system to rank potential targets:

### Scoring Factors

1. **Distance**: Closer targets receive higher scores
2. **Angle**: Targets closer to the center of view receive higher scores
3. **Visibility**: Targets in direct line of sight receive higher scores
4. **Proximity Bonus**: Very close targets receive an additional bonus

### Score Calculation

```csharp
private float CalculateScore(float distance, float angle, SphereDetectSensorComponent sensor)
{
    // Distance has 70% weight, angle has 30% weight
    float distanceScore = 1.0f - (distance / sensor.DetectionRange);
    float angleScore = 1.0f - (angle / sensor.DetectionAngle);
    
    // Add extra bonus for very close enemies (within 20% of detection range)
    float proximityBonus = distance < (sensor.DetectionRange * 0.2f) ? 0.3f : 0.0f;
    
    return (distanceScore * 0.7f) + (angleScore * 0.3f) + proximityBonus;
}
```

### Target Switching Logic

To prevent rapid target switching, the system uses hysteresis:

```csharp
// Determine if we should switch targets
bool shouldSwitchTarget = false;
if (bestTarget != Entity.Null)
{
    if (currentTarget == Entity.Null)
    {
        shouldSwitchTarget = true; // No current target, switch to best target
    }
    else if (currentTarget != bestTarget)
    {
        // Switch only if the new target is significantly better
        float scoreDifference = bestScore - currentTargetScore;
        if (scoreDifference > sensor.TargetSwitchScoreThreshold)
        {
            shouldSwitchTarget = true;
        }
    }
}
```

### Target Behind Player Detection

To handle targets behind the player, the system includes special logic:

```csharp
public bool IsAnyTargetBehindPlayer(float3 playerPosition, float3 playerForward, float detectionRange, float behindAngleThreshold)
{
    // Get all detected enemies
    var detectedEnemies = GetBuffer<DetectedEnemyBuffer>(detectionTargetEntity);
    
    for (int i = 0; i < detectedEnemies.Length; i++)
    {
        var enemy = detectedEnemies[i];
        var dirToEnemy = math.normalize(enemy.Position - playerPosition);
        var angle = math.degrees(math.acos(math.dot(dirToEnemy, playerForward)));
        
        // Check if enemy is behind player (angle > 90 degrees)
        if (angle > behindAngleThreshold && 
            math.distance(enemy.Position, playerPosition) <= detectionRange)
        {
            return true;
        }
    }
    
    return false;
}
```

## Triad Cluster System

The Triad Cluster system is an advanced target selection algorithm that groups nearby enemies into clusters to improve targeting stability.

### Cluster Components

```csharp
// Triad cluster settings
public struct TriadClusterSettingsComponent : IComponentData
{
    public float ClusterRadius;
    public float ClusterScoreMultiplier;
    public float MinClusterSize;
    public IntraClusterTargetSelectionMode SelectionMode;
}

// Cluster selection modes
public enum IntraClusterTargetSelectionMode
{
    Closest,
    CenterMost,
    HighestScore,
    LowestHealth
}
```

### Clustering Algorithm

```csharp
private NativeList<EnemyCluster> CreateClusters(NativeArray<DetectedEnemyData> enemies, float clusterRadius)
{
    var clusters = new NativeList<EnemyCluster>(Allocator.Temp);
    var processedIndices = new NativeHashSet<int>(enemies.Length, Allocator.Temp);
    
    // Process each enemy
    for (int i = 0; i < enemies.Length; i++)
    {
        if (processedIndices.Contains(i))
            continue;
            
        // Start a new cluster
        var cluster = new EnemyCluster
        {
            CenterPosition = enemies[i].Position,
            Entities = new NativeList<Entity>(10, Allocator.Temp),
            TotalScore = 0f
        };
        
        // Add the first enemy
        cluster.Entities.Add(enemies[i].Entity);
        cluster.TotalScore += enemies[i].Score;
        processedIndices.Add(i);
        
        // Find nearby enemies
        for (int j = 0; j < enemies.Length; j++)
        {
            if (i == j || processedIndices.Contains(j))
                continue;
                
            float distSq = math.distancesq(enemies[i].Position, enemies[j].Position);
            if (distSq <= clusterRadius * clusterRadius)
            {
                // Add to cluster
                cluster.Entities.Add(enemies[j].Entity);
                cluster.TotalScore += enemies[j].Score;
                processedIndices.Add(j);
                
                // Update center position
                cluster.CenterPosition = (cluster.CenterPosition * cluster.Entities.Length + enemies[j].Position) / 
                                         (cluster.Entities.Length + 1);
            }
        }
        
        // Add cluster if it has enough entities
        if (cluster.Entities.Length >= settings.MinClusterSize)
        {
            clusters.Add(cluster);
        }
        else
        {
            // Clean up small cluster
            cluster.Entities.Dispose();
        }
    }
    
    return clusters;
}
```

### Cluster-Based Target Selection

```csharp
private Entity SelectTargetFromClusters(NativeList<EnemyCluster> clusters, TriadClusterSettingsComponent settings)
{
    if (clusters.Length == 0)
        return Entity.Null;
        
    // Find best cluster
    int bestClusterIndex = 0;
    float bestClusterScore = clusters[0].TotalScore;
    
    for (int i = 1; i < clusters.Length; i++)
    {
        if (clusters[i].TotalScore > bestClusterScore)
        {
            bestClusterScore = clusters[i].TotalScore;
            bestClusterIndex = i;
        }
    }
    
    // Select target from best cluster
    var bestCluster = clusters[bestClusterIndex];
    
    switch (settings.SelectionMode)
    {
        case IntraClusterTargetSelectionMode.Closest:
            return SelectClosestEntityInCluster(bestCluster);
            
        case IntraClusterTargetSelectionMode.CenterMost:
            return SelectCenterMostEntityInCluster(bestCluster);
            
        case IntraClusterTargetSelectionMode.HighestScore:
            return SelectHighestScoreEntityInCluster(bestCluster);
            
        case IntraClusterTargetSelectionMode.LowestHealth:
            return SelectLowestHealthEntityInCluster(bestCluster);
            
        default:
            return bestCluster.Entities[0];
    }
}
```

## Performance Optimization

### Spatial Partitioning

The system uses KD-Tree spatial partitioning for efficient enemy queries:

```csharp
// Use KDTree for initial spatial query
if (sensor.UseKDTree && !kdTreeQuery.IsEmpty)
{
    var kdTreeEntity = kdTreeQuery.GetSingletonEntity();
    var kdTree = SystemAPI.GetComponent<KDTreeComponent>(kdTreeEntity);
    
    // Create NativeList for storing nearby results
    var nearbyResults = new NativeList<Position3D>(Allocator.TempJob);
    
    if (kdTree.Tree.IsCreated)
    {
        // Get a copy of the nodes to avoid disposal issues
        var nodes = kdTree.Tree.GetNodes();
        var nodesCopy = new NativeArray<Position3D>(nodes, Allocator.TempJob);
        
        // Get enemy positions and entities
        var enemyTransforms = m_EnemyQuery.ToComponentDataArray<LocalToWorld>(Allocator.TempJob);
        var enemyEntities = m_EnemyQuery.ToEntityArray(Allocator.TempJob);
        
        // First collect all positions within range
        for (int i = 0; i < nodesCopy.Length; i++)
        {
            var node = nodesCopy[i];
            if (math.distance(node.Position, playerPosition) <= sensor.DetectionRange)
            {
                nearbyResults.Add(node);
                
                // Find matching enemy entity
                for (int j = 0; j < enemyTransforms.Length; j++)
                {
                    if (math.distance(enemyTransforms[j].Position, node.Position) < 0.01f)
                    {
                        // Process enemy
                        // ...
                    }
                }
            }
        }
    }
}
```

### Detection Interval

The system throttles detection checks to reduce CPU usage:

```csharp
var currentTime = SystemAPI.Time.ElapsedTime;
var canSwitchTarget = (currentTime - lastSwitchTime) >= sensor.CheckInterval;

// Only perform full detection if enough time has passed
if (!canSwitchTarget)
{
    // Just update existing targets
    UpdateExistingTargets();
    return;
}
```

### Burst Compilation

The system uses Burst compilation for performance-critical jobs:

```csharp
[BurstCompile]
private partial struct DetectionJob : IJob
{
    [ReadOnly] public PhysicsWorld PhysicsWorld;
    [ReadOnly] public Entity PlayerEntity;
    [ReadOnly] public Entity WeaponEntity;
    [ReadOnly] public float3 PlayerPosition;
    [ReadOnly] public float3 PlayerForward;
    [ReadOnly] public float FOVAngle;
    [ReadOnly] public float DetectionRange;
    // ... other fields ...
    
    public void Execute()
    {
        // Detection logic
    }
}
```

### Parallel Processing

The system uses parallel processing for efficient detection:

```csharp
// Schedule detection job
var job = new DetectionJob
{
    // ... job parameters ...
};

var handle = job.ScheduleParallel();
handle.Complete();
```

## Common Issues and Solutions

### Issue: Rapid Target Switching

**Symptoms:**
- Player rapidly switches between targets
- Targeting feels unstable
- Difficult to focus on a specific enemy

**Solutions:**
1. Increase `TargetSwitchScoreThreshold` to require a larger score difference
2. Implement the Triad Cluster system for group-based targeting
3. Add hysteresis to favor keeping the current target
4. Increase `CheckInterval` to reduce the frequency of target updates

### Issue: Targets Behind Player

**Symptoms:**
- Player cannot target enemies behind them
- Must manually turn to face enemies behind
- Surprise attacks from behind are difficult to respond to

**Solutions:**
1. Implement 360-degree detection with angle-based scoring
2. Add special handling for close enemies behind the player
3. Implement a "threat detection" system for enemies behind the player
4. Add UI indicators for enemies outside the field of view

### Issue: Performance with Many Enemies

**Symptoms:**
- FPS drops when many enemies are present
- Detection system becomes a bottleneck
- CPU usage spikes during detection updates

**Solutions:**
1. Use KD-Tree spatial partitioning for efficient queries
2. Increase `CheckInterval` to reduce update frequency
3. Implement distance-based processing (less frequent updates for distant enemies)
4. Optimize physics queries with better collision layers

### Issue: Detection Through Walls

**Symptoms:**
- Enemies are detected through walls
- Targeting works through obstacles
- Unrealistic detection behavior

**Solutions:**
1. Use physics raycasts to check line of sight
2. Implement proper collision layers for detection
3. Add visibility checks to the scoring algorithm
4. Use navmesh-based distance instead of direct distance

## Implementation Examples

### Implementing Behind-Player Detection

```csharp
// In AimingModule.cs
public bool IsAnyTargetBehindPlayer()
{
    var playerTransform = GetComponent<PlayerHeadTransformComponent>();
    var sensor = GetComponent<SphereDetectSensorComponent>();
    
    float3 playerPosition = playerTransform.Position;
    float3 playerForward = playerTransform.Forward;
    
    // Define "behind" as more than 120 degrees from forward
    float behindAngleThreshold = 120f;
    
    // Check for enemies behind player
    return detectionSystem.IsAnyTargetBehindPlayer(
        playerPosition, 
        playerForward, 
        sensor.DetectionRange, 
        behindAngleThreshold);
}

// In MovementModule.cs
public void Update()
{
    // Check if there's a target behind the player
    if (aimingModule.IsAnyTargetBehindPlayer())
    {
        // Quickly rotate to face the threat
        RotateToFaceBehindTarget();
    }
    
    // Normal movement logic
    // ...
}

private void RotateToFaceBehindTarget()
{
    // Get the target position
    var detectionTarget = SystemAPI.GetSingleton<DetectionTargetComponent>();
    if (!detectionTarget.HasTarget) return;
    
    // Calculate direction to target
    var playerTransform = GetComponent<PlayerTransform>();
    float3 directionToTarget = math.normalize(detectionTarget.CurrentPosition - playerTransform.Position);
    
    // Calculate target rotation
    quaternion targetRotation = quaternion.LookRotation(directionToTarget, math.up());
    
    // Apply quick rotation
    playerTransform.Rotation = math.slerp(
        playerTransform.Rotation,
        targetRotation,
        fastRotationSpeed * Time.deltaTime);
}
```

### Implementing Triad Cluster Targeting

```csharp
// In your detection system
public partial class EnhancedDetectionSystem : SystemBase
{
    private EntityQuery triadClusterSettingsQuery;
    
    protected override void OnCreate()
    {
        // ... other initialization ...
        
        triadClusterSettingsQuery = GetEntityQuery(ComponentType.ReadOnly<TriadClusterSettingsComponent>());
    }
    
    protected override void OnUpdate()
    {
        // ... detection logic ...
        
        // Get detected enemies
        var detectedEnemies = new NativeList<DetectedEnemyData>(Allocator.TempJob);
        
        // ... populate detected enemies ...
        
        // Check if we should use cluster targeting
        if (!triadClusterSettingsQuery.IsEmpty)
        {
            var settings = triadClusterSettingsQuery.GetSingleton<TriadClusterSettingsComponent>();
            
            // Create clusters
            var clusters = CreateClusters(detectedEnemies, settings.ClusterRadius);
            
            // Select target from clusters
            Entity bestTarget = SelectTargetFromClusters(clusters, settings);
            
            // Update current target
            if (bestTarget != Entity.Null)
            {
                // Remove current target tag from all entities
                foreach (var entity in currentTargetTagQuery.ToEntityArray(Allocator.Temp))
                {
                    EntityManager.RemoveComponent<CurrentTargetTag>(entity);
                }
                
                // Add tag to new target
                EntityManager.AddComponent<CurrentTargetTag>(bestTarget);
            }
            
            // Clean up
            foreach (var cluster in clusters)
            {
                cluster.Entities.Dispose();
            }
            clusters.Dispose();
        }
        else
        {
            // Use standard targeting
            // ...
        }
        
        // ... cleanup ...
    }
}
```

## Troubleshooting

### No Targets Detected

**Check List:**
1. Verify `DetectionRange` and `DetectionAngle` are appropriate
2. Check that enemies have the required components (EnemyTag, LocalToWorld, etc.)
3. Verify physics layers are set correctly for collision detection
4. Check that enemies are not marked as `UndetectableTag` or `DeadTag`
5. Verify KD-Tree is properly initialized if using spatial partitioning

### Unstable Target Selection

**Check List:**
1. Check `TargetSwitchScoreThreshold` and increase if necessary
2. Verify `CheckInterval` is not too short (should be at least 0.1-0.2 seconds)
3. Implement or adjust Triad Cluster system
4. Check for rapid enemy movement that might cause score fluctuations
5. Verify scoring algorithm weights are appropriate

### Performance Issues

**Check List:**
1. Enable KD-Tree spatial partitioning
2. Increase `CheckInterval` to reduce update frequency
3. Optimize physics queries with better collision layers
4. Implement distance-based processing
5. Profile the detection system to identify specific bottlenecks
