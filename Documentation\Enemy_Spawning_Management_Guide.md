# Enemy Spawning and Management Guide

This document provides detailed information about the enemy spawning and management systems in the project, focusing on optimizing performance and addressing issues with entity count and lifecycle management.

## Table of Contents

1. [Overview](#overview)
2. [Spawning System Architecture](#spawning-system-architecture)
3. [Enemy Lifecycle Management](#enemy-lifecycle-management)
4. [Crowds Navigation Integration](#crowds-navigation-integration)
5. [Performance Optimization](#performance-optimization)
6. [Common Issues and Solutions](#common-issues-and-solutions)
7. [Implementation Examples](#implementation-examples)
8. [Troubleshooting](#troubleshooting)

## Overview

The enemy spawning and management system is responsible for:

1. **Creating enemies** at runtime based on configuration
2. **Managing enemy lifecycle** from spawn to death
3. **Controlling entity count** to maintain performance
4. **Integrating with navigation** for enemy movement
5. **Optimizing performance** for mobile devices

The system uses a combination of ECS components and systems to efficiently manage large numbers of enemies.

## Spawning System Architecture

### Core Components

```csharp
// Spawn component for tracking when an entity was spawned
public struct SpawnComponent : IComponentData
{
    public float SpawnTime;    // Time when the entity was spawned
}

// Enemy spawn configuration
public struct EnemySpawnComponent : IComponentData
{
    public Entity enemyPrefab;
    public float spawnInterval;
    public float spawnRadius;
    public float playerAvoidRadius;
    public int spawnBatchSize;
    public int maxEnemyCount; // Maximum number of enemies allowed
    public int currentEnemyCount; // Current number of spawned enemies
    public float timeSinceLastSpawn;
}

// Crowds navigation spawner
public struct Spawner : IComponentData
{
    public Entity Group;
    public Entity Prefab;
    public float Interval;
    public int Batch;
    public float3 Size;
    public int Count;
    public int MaxCount;
    public Unity.Mathematics.Random Random;
    public float Elapsed;
    public float3 Destination;
}
```

### Key Systems

#### SpawnTrackingSystem

The `SpawnTrackingSystem` adds `SpawnComponent` to newly spawned entities to track their spawn time:

```csharp
[UpdateInGroup(typeof(InitializationSystemGroup))]
public partial class SpawnTrackingSystem : SystemBase
{
    private EntityQuery _newEntitiesQuery;
    private HashSet<Entity> _processedEntities = new HashSet<Entity>();

    protected override void OnCreate()
    {
        // Query for entities that have essential components but no SpawnComponent yet
        _newEntitiesQuery = GetEntityQuery(
            ComponentType.ReadOnly<LocalToWorld>(),
            ComponentType.ReadOnly<HealthComponent>(),
            ComponentType.Exclude<SpawnComponent>(),
            ComponentType.Exclude<DeadTag>()
        );
    }

    protected override void OnUpdate()
    {
        // Get current time
        float currentTime = (float)SystemAPI.Time.ElapsedTime;
        
        // Create command buffer
        var ecb = new EntityCommandBuffer(Allocator.TempJob);
        
        // Get all entities that need a SpawnComponent
        var entities = _newEntitiesQuery.ToEntityArray(Allocator.Temp);
        
        foreach (var entity in entities)
        {
            // Skip if already processed
            if (_processedEntities.Contains(entity))
                continue;
                
            // Add SpawnComponent with current time
            ecb.AddComponent(entity, new SpawnComponent
            {
                SpawnTime = currentTime
            });
            
            // Add to processed set
            _processedEntities.Add(entity);
            
            DebugLogManager.Instance.Log($"Added SpawnComponent to entity {entity.Index} at time {currentTime}");
        }
        
        // Play back commands
        ecb.Playback(EntityManager);
        ecb.Dispose();
        entities.Dispose();
        
        // Clean up processed entities set periodically
        if (_processedEntities.Count > 1000)
        {
            CleanupProcessedEntities();
        }
    }
    
    private void CleanupProcessedEntities()
    {
        // Remove entities that no longer exist
        _processedEntities.RemoveWhere(entity => !EntityManager.Exists(entity));
    }
}
```

#### CharacterSpawnerSystem

The `CharacterSpawnerSystem` handles spawning enemies using the Crowds navigation system:

```csharp
[UpdateInGroup(typeof(FixedStepSimulationSystemGroup))]
[UpdateBefore(typeof(AgentSystemGroup))]
public partial struct CharacterSpawnerSystem : ISystem
{
    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        // Get the entity command buffer
        var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>();
        
        // Create a new job to handle spawning
        var spawnerJob = new SpawnerJob
        {
            Ecb = ecb.CreateCommandBuffer(state.WorldUnmanaged).AsParallelWriter(),
            DeltaTime = state.WorldUnmanaged.Time.DeltaTime
        };
        
        // Schedule the job
        state.Dependency = spawnerJob.ScheduleParallel(state.Dependency);
    }
    
    // Job to handle spawning characters
    [BurstCompile]
    private partial struct SpawnerJob : IJobEntity
    {
        // Entity command buffer for creating entities
        public EntityCommandBuffer.ParallelWriter Ecb;
        
        // Delta time for this frame
        public float DeltaTime;
        
        // Execute for each entity with the required components
        public void Execute(ref Spawner spawner, in LocalTransform transform, [EntityIndexInQuery] int sortKey)
        {
            // If we've reached the maximum number of characters, don't spawn any more
            if (spawner.MaxCount == spawner.Count)
                return;
            
            // Update the elapsed time
            spawner.Elapsed += DeltaTime;
            
            // Check if it's time to spawn
            if (spawner.Elapsed >= spawner.Interval)
            {
                // Reset the elapsed time
                spawner.Elapsed = 0;
                
                // Calculate how many characters to spawn
                int spawnCount = math.min(spawner.Batch, spawner.MaxCount - spawner.Count);
                
                // Update the count
                spawner.Count += spawnCount;
                
                // Spawn the characters
                for (int i = 0; i < spawnCount; i++)
                {
                    // Generate a random position within the spawn area
                    float3 offset = spawner.Random.NextFloat3(-spawner.Size, spawner.Size);
                    float3 position = transform.Position + offset;
                    
                    // Instantiate the character
                    Entity character = Ecb.Instantiate(sortKey, spawner.Prefab);
                    
                    // Set the character's position
                    Ecb.SetComponent(sortKey, character, new LocalTransform 
                    { 
                        Position = position, 
                        Scale = 1, 
                        Rotation = quaternion.identity 
                    });
                    
                    // Set the character's destination
                    Ecb.SetComponent(sortKey, character, new AgentBody 
                    { 
                        Destination = spawner.Destination, 
                        IsStopped = false 
                    });
                    
                    // Set the character's crowd group
                    Ecb.SetSharedComponent(sortKey, character, new AgentCrowdPath 
                    { 
                        Group = spawner.Group 
                    });
                }
            }
        }
    }
}
```

#### OptimizedSpawnerSystem

The `OptimizedSpawnerSystem` is a legacy system that handles spawning without Crowds navigation:

```csharp
public partial class OptimizedSpawnerSystem : SystemBase
{
    private EntityQuery playerQuery;
    private EntityQuery enemyQuery;
    
    protected override void OnCreate()
    {
        playerQuery = GetEntityQuery(ComponentType.ReadOnly<PlayerTag>(), ComponentType.ReadOnly<LocalToWorld>());
        enemyQuery = GetEntityQuery(ComponentType.ReadOnly<EnemyTag>(), ComponentType.Exclude<DeadTag>());
    }
    
    protected override void OnUpdate()
    {
        if (playerQuery.IsEmpty)
            return;
            
        var playerEntity = playerQuery.GetSingletonEntity();
        var playerTransform = EntityManager.GetComponentData<LocalToWorld>(playerEntity);
        var playerPosition = playerTransform.Position;
        
        // Process each spawner
        Entities.ForEach((Entity entity, ref EnemySpawnComponent spawner) =>
        {
            // Update timer
            spawner.timeSinceLastSpawn += Time.DeltaTime;
            
            // Check if it's time to spawn
            if (spawner.timeSinceLastSpawn >= spawner.spawnInterval)
            {
                spawner.timeSinceLastSpawn = 0f;
                
                // Get current enemy count
                int currentEnemyCount = enemyQuery.CalculateEntityCount();
                spawner.currentEnemyCount = currentEnemyCount;
                
                // Check if we can spawn more enemies
                if (currentEnemyCount < spawner.maxEnemyCount)
                {
                    // Calculate how many to spawn
                    int spawnCount = math.min(spawner.spawnBatchSize, spawner.maxEnemyCount - currentEnemyCount);
                    
                    // Spawn enemies
                    for (int i = 0; i < spawnCount; i++)
                    {
                        // Generate random position
                        float angle = UnityEngine.Random.Range(0f, 2f * math.PI);
                        float distance = UnityEngine.Random.Range(spawner.playerAvoidRadius, spawner.spawnRadius);
                        float3 offset = new float3(
                            math.cos(angle) * distance,
                            0f,
                            math.sin(angle) * distance
                        );
                        float3 spawnPosition = playerPosition + offset;
                        
                        // Instantiate enemy
                        Entity enemyEntity = EntityManager.Instantiate(spawner.enemyPrefab);
                        
                        // Set position
                        EntityManager.SetComponentData(enemyEntity, new LocalTransform
                        {
                            Position = spawnPosition,
                            Rotation = quaternion.identity,
                            Scale = 1f
                        });
                    }
                }
                else if (currentEnemyCount > spawner.maxEnemyCount)
                {
                    // Too many enemies, destroy some
                    int excessCount = currentEnemyCount - spawner.maxEnemyCount;
                    DestroyExcessEnemies(excessCount);
                }
            }
        }).WithoutBurst().Run();
    }
    
    private void DestroyExcessEnemies(int count)
    {
        // Get all enemies
        var enemies = enemyQuery.ToEntityArray(Allocator.Temp);
        
        // Destroy the oldest enemies first (or random if no age tracking)
        for (int i = 0; i < math.min(count, enemies.Length); i++)
        {
            EntityManager.DestroyEntity(enemies[i]);
        }
        
        enemies.Dispose();
    }
}
```

## Enemy Lifecycle Management

### Spawn Phase

1. **Entity Creation**: Enemy entity is instantiated from prefab
2. **Position Setting**: Entity is positioned based on spawn configuration
3. **Spawn Tracking**: `SpawnComponent` is added with current time
4. **Initialization**: Navigation and other components are initialized

### Active Phase

1. **Movement**: Enemy navigates using Crowds navigation
2. **Animation**: Enemy animations are updated based on movement
3. **Detection**: Enemy can be detected by player
4. **Combat**: Enemy can attack and be damaged

### Death Phase

1. **Death Marking**: `DeadTag` is added when health reaches zero
2. **Animation**: Death animation is played
3. **Dissolve Effect**: Dissolve effect is applied after animation
4. **Cleanup**: Entity is destroyed after dissolve effect

## Crowds Navigation Integration

The system integrates with the Agents Navigation - Crowds package for efficient pathfinding:

### Setup Components

```csharp
// Agent body for navigation
public struct AgentBody : IComponentData
{
    public float3 Destination;
    public bool IsStopped;
}

// Agent crowd path for group-based navigation
public struct AgentCrowdPath : ISharedComponentData
{
    public Entity Group;
}

// Agent settings
public struct AgentSettings : IComponentData
{
    public float Speed;
    public float Acceleration;
    public float StoppingDistance;
    public float AvoidanceRadius;
}
```

### Navigation Update

```csharp
// In EnemyMovementSystem
[BurstCompile]
public void OnUpdate(ref SystemState state)
{
    float deltaTime = SystemAPI.Time.DeltaTime;
    
    float3 playerPosition = float3.zero;
    bool playerFound = false;

    // Query the player position
    foreach (var playerTransform in SystemAPI.Query<RefRO<PlayerTransform>>())
    {
        playerPosition = playerTransform.ValueRO.Position;
        playerFound = true;
        break; // Assuming only one player entity exists
    }

    if (!playerFound) return;
    
    // Update destination for all enemies
    foreach (var body in SystemAPI.Query<RefRW<SetDestination>>())
    {
        body.ValueRW.Value = playerPosition;
    }
}
```

## Performance Optimization

### Entity Count Management

The system manages entity count to maintain performance:

```csharp
// In CharacterSpawnerSystem
public void SetMaxEntityCount(int maxCount)
{
    // Update all spawners
    Entities
        .ForEach((ref Spawner spawner) =>
        {
            spawner.MaxCount = maxCount;
            
            // If current count exceeds new max, mark excess entities for destruction
            if (spawner.Count > maxCount)
            {
                DestroyExcessEntities(spawner.Count - maxCount);
                spawner.Count = maxCount;
            }
        })
        .WithoutBurst()
        .Run();
}

private void DestroyExcessEntities(int count)
{
    // Get all enemies
    var enemies = GetEntityQuery(
        ComponentType.ReadOnly<EnemyTag>(),
        ComponentType.Exclude<DeadTag>()
    ).ToEntityArray(Allocator.Temp);
    
    // Sort by distance from player (destroy furthest first)
    SortEntitiesByDistanceFromPlayer(ref enemies);
    
    // Destroy excess entities
    var ecb = new EntityCommandBuffer(Allocator.Temp);
    for (int i = 0; i < math.min(count, enemies.Length); i++)
    {
        ecb.DestroyEntity(enemies[i]);
    }
    
    ecb.Playback(EntityManager);
    ecb.Dispose();
    enemies.Dispose();
}
```

### Spawn Rate Control

The system controls spawn rate to distribute load:

```csharp
// In CharacterSpawnerSystem
public void SetSpawnRate(float spawnInterval, int batchSize)
{
    // Update all spawners
    Entities
        .ForEach((ref Spawner spawner) =>
        {
            spawner.Interval = spawnInterval;
            spawner.Batch = batchSize;
        })
        .WithoutBurst()
        .Run();
}
```

### Distance-Based Spawning

The system can spawn enemies based on distance from player:

```csharp
// In SpawnerJob
private float3 GetSpawnPosition(float3 playerPosition, float minDistance, float maxDistance)
{
    // Generate random angle
    float angle = spawner.Random.NextFloat(0, math.PI * 2);
    
    // Generate random distance between min and max
    float distance = spawner.Random.NextFloat(minDistance, maxDistance);
    
    // Calculate position
    float3 offset = new float3(
        math.cos(angle) * distance,
        0,
        math.sin(angle) * distance
    );
    
    return playerPosition + offset;
}
```

### Spawn Delay for UI

The system implements a spawn delay to prevent UI issues:

```csharp
// In ProcessDetectedEnemies method of OptimizedDetectionSystem
// Check if this is a newly spawned entity
bool isNewlySpawned = false;
float spawnTime = 0f;

if (EntityManager.HasComponent<SpawnComponent>(enemy.Entity))
{
    var spawnComponent = EntityManager.GetComponentData<SpawnComponent>(enemy.Entity);
    spawnTime = spawnComponent.SpawnTime;
    float timeSinceSpawn = currentTime - spawnTime;
    
    // Consider entities spawned in the last 0.5 seconds as "newly spawned"
    isNewlySpawned = timeSinceSpawn < 0.5f;
    
    if (isNewlySpawned)
    {
        // Skip adding DetectedTag to newly spawned entities
        // This prevents health bars from showing immediately after spawn
        Debug.Log($"<color=yellow>Skipping detection for newly spawned entity {enemy.Entity.Index} (spawned {timeSinceSpawn:F2}s ago)</color>");
        continue;
    }
}

EntityManager.AddComponent<DetectedTag>(enemy.Entity);
```

## Common Issues and Solutions

### Issue: Too Many Entities Spawned

**Symptoms:**
- Performance drops significantly
- Entity count exceeds configured maximum
- Memory usage grows uncontrollably

**Solutions:**
1. Verify `maxEnemyCount` is respected in all spawner systems
2. Implement dynamic entity count based on device performance
3. Add safety checks to prevent excessive spawning
4. Implement entity pooling instead of instantiation/destruction

### Issue: Enemies Spawning Inside Objects

**Symptoms:**
- Enemies appear inside walls or other obstacles
- Enemies get stuck immediately after spawning
- Navigation errors after spawn

**Solutions:**
1. Use navmesh sampling for spawn positions
2. Implement physics checks before finalizing spawn position
3. Add a small vertical offset to spawn position
4. Implement spawn area validation

### Issue: Uneven Spawn Distribution

**Symptoms:**
- Enemies cluster in certain areas
- Some areas have no enemies
- Spawn pattern is predictable

**Solutions:**
1. Implement better random distribution algorithms
2. Use multiple spawn points around the player
3. Consider tactical spawn positioning
4. Implement minimum distance between spawn positions

### Issue: Health Bars Showing on Spawn

**Symptoms:**
- Health bars briefly appear on newly spawned enemies
- Health bars flicker on and off for new enemies
- UI elements show before enemies are fully initialized

**Solutions:**
1. Add spawn grace period before detection
2. Implement `SpawnComponent` to track spawn time
3. Delay adding `DetectedTag` until after grace period
4. Ensure health bar system respects spawn status

## Implementation Examples

### Dynamic Entity Count Based on Performance

```csharp
public class DynamicEntityManager : MonoBehaviour
{
    public float targetFrameRate = 30f;
    public int minEntityCount = 20;
    public int maxEntityCount = 100;
    public int adjustmentStep = 5;
    
    private float frameRateCheckInterval = 1f;
    private float timeSinceLastCheck = 0f;
    private int currentEntityLimit;
    
    private void Start()
    {
        // Start with a conservative limit
        currentEntityLimit = Mathf.FloorToInt((minEntityCount + maxEntityCount) / 2f);
        UpdateEntityLimit(currentEntityLimit);
    }
    
    private void Update()
    {
        timeSinceLastCheck += Time.deltaTime;
        
        if (timeSinceLastCheck >= frameRateCheckInterval)
        {
            float currentFPS = 1f / Time.smoothDeltaTime;
            
            // Adjust entity limit based on performance
            if (currentFPS < targetFrameRate * 0.8f)
            {
                // Performance is poor, reduce entity limit
                currentEntityLimit = Mathf.Max(minEntityCount, currentEntityLimit - adjustmentStep);
                UpdateEntityLimit(currentEntityLimit);
            }
            else if (currentFPS > targetFrameRate * 1.2f && currentEntityLimit < maxEntityCount)
            {
                // Performance is good, try increasing entity limit
                currentEntityLimit = Mathf.Min(maxEntityCount, currentEntityLimit + adjustmentStep);
                UpdateEntityLimit(currentEntityLimit);
            }
            
            timeSinceLastCheck = 0f;
        }
    }
    
    private void UpdateEntityLimit(int newLimit)
    {
        // Update the entity limit in the spawner system
        var world = World.DefaultGameObjectInjectionWorld;
        var spawnerSystem = world.GetExistingSystemManaged<CharacterSpawnerSystem>();
        spawnerSystem.SetMaxEntityCount(newLimit);
        
        Debug.Log($"Adjusted entity limit to {newLimit} based on performance");
    }
}
```

### Navmesh-Based Spawn Position

```csharp
private float3 GetNavmeshSpawnPosition(float3 playerPosition, float minDistance, float maxDistance)
{
    // Generate random direction
    float angle = UnityEngine.Random.Range(0f, 2f * math.PI);
    float3 direction = new float3(math.cos(angle), 0, math.sin(angle));
    
    // Generate random distance
    float distance = UnityEngine.Random.Range(minDistance, maxDistance);
    
    // Calculate potential position
    float3 potentialPosition = playerPosition + direction * distance;
    
    // Sample navmesh for valid position
    NavMeshHit hit;
    if (NavMesh.SamplePosition(potentialPosition, out hit, 10f, NavMesh.AllAreas))
    {
        return hit.position;
    }
    
    // Fallback to original position if navmesh sampling fails
    return potentialPosition;
}
```

### Entity Pooling System

```csharp
public class EntityPoolSystem : SystemBase
{
    private NativeList<Entity> _inactivePool;
    private int _poolSize = 150;
    private Entity _prefabEntity;
    
    protected override void OnCreate()
    {
        _inactivePool = new NativeList<Entity>(_poolSize, Allocator.Persistent);
        
        // Get prefab entity
        var spawnerQuery = GetEntityQuery(ComponentType.ReadOnly<EnemySpawnComponent>());
        if (!spawnerQuery.IsEmpty)
        {
            var spawner = spawnerQuery.GetSingleton<EnemySpawnComponent>();
            _prefabEntity = spawner.enemyPrefab;
            
            // Pre-populate pool
            PrePopulatePool();
        }
    }
    
    private void PrePopulatePool()
    {
        if (_prefabEntity == Entity.Null)
            return;
            
        for (int i = 0; i < _poolSize; i++)
        {
            var entity = EntityManager.Instantiate(_prefabEntity);
            
            // Deactivate entity
            EntityManager.AddComponent<InactiveTag>(entity);
            
            // Move far away
            if (EntityManager.HasComponent<LocalTransform>(entity))
            {
                var transform = EntityManager.GetComponentData<LocalTransform>(entity);
                transform.Position = new float3(10000, 10000, 10000);
                EntityManager.SetComponentData(entity, transform);
            }
            
            // Add to pool
            _inactivePool.Add(entity);
        }
    }
    
    public Entity GetEntityFromPool()
    {
        if (_inactivePool.Length > 0)
        {
            var entity = _inactivePool[_inactivePool.Length - 1];
            _inactivePool.RemoveAt(_inactivePool.Length - 1);
            
            // Activate entity
            EntityManager.RemoveComponent<InactiveTag>(entity);
            
            return entity;
        }
        
        // Pool is empty, create new entity
        return EntityManager.Instantiate(_prefabEntity);
    }
    
    public void ReturnEntityToPool(Entity entity)
    {
        // Deactivate entity
        EntityManager.AddComponent<InactiveTag>(entity);
        
        // Move far away
        if (EntityManager.HasComponent<LocalTransform>(entity))
        {
            var transform = EntityManager.GetComponentData<LocalTransform>(entity);
            transform.Position = new float3(10000, 10000, 10000);
            EntityManager.SetComponentData(entity, transform);
        }
        
        // Return to pool if not full
        if (_inactivePool.Length < _poolSize)
        {
            _inactivePool.Add(entity);
        }
        else
        {
            // Pool is full, destroy entity
            EntityManager.DestroyEntity(entity);
        }
    }
    
    protected override void OnUpdate()
    {
        // This system doesn't need an update method
    }
    
    protected override void OnDestroy()
    {
        if (_inactivePool.IsCreated)
        {
            _inactivePool.Dispose();
        }
    }
}
```

## Troubleshooting

### No Enemies Spawning

**Check List:**
1. Verify `enemyPrefab` is properly set in `EnemySpawnComponent`
2. Check that `spawnInterval` is not too large
3. Verify `maxEnemyCount` is greater than zero
4. Check for errors in spawn position calculation
5. Verify that the spawner entity exists and has the required components

### Enemies Spawning Too Quickly

**Check List:**
1. Check `spawnInterval` value in `EnemySpawnComponent`
2. Verify `spawnBatchSize` is appropriate
3. Check for multiple spawner entities that might be spawning simultaneously
4. Verify time scaling is not affecting spawn rate
5. Check for logic errors in spawn timing calculation

### Enemies Not Moving After Spawn

**Check List:**
1. Verify Crowds navigation components are properly set
2. Check that `AgentBody` has a valid destination
3. Verify `AgentCrowdPath` has a valid group
4. Check for obstacles at spawn positions
5. Verify navmesh is properly baked and accessible

### Entity Count Exceeding Maximum

**Check List:**
1. Verify `maxEnemyCount` is respected in all spawner systems
2. Check for multiple spawner systems running simultaneously
3. Verify entity destruction logic is working properly
4. Check for logic errors in entity counting
5. Implement additional safety checks to enforce maximum
