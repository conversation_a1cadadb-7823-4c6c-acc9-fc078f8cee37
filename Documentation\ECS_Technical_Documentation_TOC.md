# ECS Technical Documentation - Table of Contents

This documentation provides a comprehensive guide to the Entity Component System (ECS) architecture used in the project. It is intended for developers who need to understand, maintain, or extend the ECS-based systems.

## Documentation Sections

1. [**Overview**](ECS_Technical_Documentation_1_Overview.md)
   - ECS Architecture Overview
   - Core Components
   - Main Systems
   - System Groups
   - Performance Considerations

2. [**Detection and Targeting**](ECS_Technical_Documentation_2_Detection.md)
   - Detection Components
   - Detection Systems
   - Targeting Systems
   - Spatial Partitioning
   - Implementation Guidelines

3. [**Weapons and Shooting**](ECS_Technical_Documentation_3_Weapons.md)
   - Weapon Components
   - Weapon Configuration
   - Weapon Systems
   - Bullet Systems
   - Implementation Guidelines

4. [**Health and Damage**](ECS_Technical_Documentation_4_Health.md)
   - Health Components
   - Damage Systems
   - Health Bar Systems
   - Death Systems
   - Implementation Guidelines

5. [**Animation**](ECS_Technical_Documentation_5_Animation.md)
   - Animation Components
   - Animation Systems
   - Animation Integration
   - Special Animation Effects
   - Implementation Guidelines

6. [**Enemy Spawning and AI**](ECS_Technical_Documentation_6_Spawning.md)
   - Spawning Components
   - Spawning Systems
   - Navigation and Movement
   - AI Behavior
   - Performance Optimization
   - Implementation Guidelines

7. [**Spatial Partitioning and Optimization**](ECS_Technical_Documentation_7_SpatialPartitioning.md)
   - Spatial Partitioning Components
   - KD-Tree System
   - Spatial Hash Grid System
   - Performance Optimization
   - Implementation Guidelines

8. [**Debugging and Implementation Guidelines**](ECS_Technical_Documentation_8_Debugging.md)
   - Debugging Tools
   - Logging System
   - Performance Monitoring
   - Implementation Guidelines
   - Common Issues and Solutions

## How to Use This Documentation

- **New Developers**: Start with the Overview section to understand the basic architecture, then explore specific systems as needed.
- **Feature Developers**: Focus on the section related to the feature you're working on, and refer to the Implementation Guidelines for best practices.
- **Performance Optimization**: Review the Performance Considerations in the Overview and the dedicated Optimization section.
- **Debugging**: Refer to the Debugging and Implementation Guidelines section for troubleshooting and common issues.

## Key Systems Overview

| System Category | Key Systems | Description |
|----------------|-------------|-------------|
| Detection | OptimizedDetectionSystem | Detects enemies within the player's field of view |
| Targeting | AimingModule, TriadClusterSystem | Selects the best target from detected enemies |
| Weapons | OptimizedShootingSystem, WeaponAimSystem | Handles weapon firing and bullet creation |
| Health | DamageSystem, HealthBarSpawnSystem | Manages entity health and damage |
| Animation | EnemyAnimationSystem, HitAnimationSystem | Controls character animations |
| Spawning | OptimizedSpawnerSystem, SpawnerSystem | Creates and manages enemy entities |
| Spatial | KDTreeSystem, SpatialHashGridSystem | Optimizes spatial queries for entity finding |
| Debugging | DebugLogManager, PerformanceMonitor | Provides tools for debugging and monitoring |

## Implementation Checklist

When implementing new features, ensure you:

1. ✅ Follow the component-based design pattern
2. ✅ Use appropriate system update groups and ordering
3. ✅ Optimize for performance with Burst compilation and jobs
4. ✅ Handle entity creation and destruction properly
5. ✅ Add appropriate logging and debugging support
6. ✅ Document your implementation for other developers
7. ✅ Test thoroughly, especially on target platforms
