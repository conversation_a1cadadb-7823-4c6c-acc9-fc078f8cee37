# Animation Module Documentation

## Overview

The `AnimationModule` is responsible for managing all player animations, including state transitions, blending, and layering. It provides a flexible system for playing animations based on the player's current state and actions.

## Features

- Manages animation states and transitions
- Provides a state machine for complex animation sequences
- Handles animation blending and layering
- Supports both Animator and Animancer animation systems
- Provides methods for setting animation parameters

## Implementation

The `AnimationModule` is implemented through several classes:

### CharacterAnimationModule

```csharp
public class CharacterAnimationModule : AnimationModuleWithStates<CharacterState, MovementSubState>
{
    /// <summary>
    /// Applies rotation through animation instead of transform rotation
    /// </summary>
    /// <param name="rotateAmount">Amount to rotate in degrees</param>
    public void InPlaceRotation(float rotateAmount)
    {
        // Implementation
    }
    
    // Additional animation methods
    // ...
}
```

### AnimationModuleWithStates

```csharp
public class AnimationModuleWithStates<T,T1> : SerializedMonoBehaviour,
    IAnimationModuleWithStates<T,T1>
    where T : State where T1 : Enum
{
    [SerializeField] private bool m_useAnimancer;
    [field:SerializeField] public int ControllerIndex { get; private set; }
    [field: SerializeField] public List<MainState> MainState { get; set; }
    [field: SerializeField] public T1 SubState { get; set; }

    [field: SerializeField] public bool HasRefreshRate { get; private set; }
    [field: SerializeField] public float RefreshRate { get; private set; }

    public Enum GetModuleSubState() => SubState;

    [field: SerializeField] public Dictionary<Enum, T> States { get; set; }
    
    // Animation methods
    // ...
}
```

## Animation State Machine

The `AnimationModule` uses a state machine to manage animation states:

```csharp
public class StateMachine<T> where T : State
{
    private T _currentState;
    public T CurrentState => _currentState;
    public T PreviousState { get; private set; }

    public bool CanSetState(State state)
    {
        return _currentState == null || _currentState.CanExitState;
    }

    public bool TrySetState(State state, float crossFadeDuration, Object conditions = null)
    {
        if (CanSetState(state))
        {
            SetState(state, crossFadeDuration, conditions);
            return true;
        }
        return false;
    }

    public void SetState(State state, float crossFadeDuration, Object conditions = null)
    {
        if (state == null) throw new ArgumentNullException(nameof(state));

        _currentState?.OnExitState();
        PreviousState = _currentState;
        if(PreviousState != null)
            PreviousState.enabled = false;
        _currentState = state;
        _currentState.enabled = true;
        _currentState.OnEnterState(crossFadeDuration, conditions);
    }

    public void ForceSetState(State state, float crossFadeDuration = 0.25f) 
    {
        if (state == null) throw new ArgumentNullException(nameof(state));

        _currentState?.OnExitState();
        PreviousState = _currentState;
        _currentState = state;
        _currentState.OnEnterState(crossFadeDuration);
    }
}
```

## Animation States

Each animation state is represented by a `State` class:

```csharp
public abstract class State : MonoBehaviour
{
    public string Name => GetType().Name;
    public bool CanExitState { get; protected set; } = true;

    public virtual void OnEnterState(float crossFadeDuration, object conditions = null) { }
    public virtual void OnExitState() { }
    public virtual void OnUpdate() { }
}
```

### Character States

```csharp
public class CharacterState : State
{
    [SerializeField] protected Animator animator;
    [SerializeField] protected string animationName;
    [SerializeField] protected float crossFadeDuration = 0.25f;
    
    public override void OnEnterState(float crossFadeDuration, object conditions = null)
    {
        base.OnEnterState(crossFadeDuration, conditions);
        
        // Play the animation
        animator.CrossFade(animationName, crossFadeDuration);
    }
    
    public override void OnExitState()
    {
        base.OnExitState();
        
        // Clean up any animation state
    }
}
```

## Animation Interfaces

The `AnimationModule` implements several interfaces:

### IAnimationModule

```csharp
public interface IAnimationModule : IModule<AnimationSubState>
{
    void PlayAnimation(string animationName);
    void PlayAnimation(string animationName, float crossFadeDuration);
    bool IsAnimationPlaying(string animationName);
    IEnumerator WaitForAnimation(string animationName);
    bool IsPlayingAnimation { get; }
    void SetFloat(string parameterName, float value, float dampTime, float deltaTime);
    void SetFloat(string parameterName, float value);
    float GetFloat(string parameterName);
    void SetBool(string parameterName, bool value);
    void SetTrigger(string parameterName);
    void SetLayerWeight(string layerName, float value);
}
```

### IAnimationModuleWithStates

```csharp
public interface IAnimationModuleWithStates<T,T1> : IModule<T1>
{
    public StateMachine<State> StateMachine { get; }
    Dictionary<Enum, T> States { get; set; }
    void PlayAnimation(string animationName);
    void PlayAnimationState(Enum animationNameState, float crossFadeDuration, Object conditions = null);
    void PlayAnimation(string animationName, float crossFadeDuration);
    bool IsAnimationPlaying(string animationName);
    IEnumerator WaitForAnimation(string animationName);
    bool IsPlayingAnimation { get; }
    void SetFloat(string parameterName, float value, float dampTime, float deltaTime);
    void SetFloat(string parameterName, float value);
    float GetFloat(string parameterName);
    void SetBool(string parameterName, bool value);
    void SetTrigger(string parameterName);
    void SetLayerWeight(string layerName, float value);
}
```

## Animation Methods

The `AnimationModule` provides several methods for controlling animations:

### Playing Animations

```csharp
public void PlayAnimation(string animationName)
{
    if (m_useAnimancer)
    {
        // Play animation using Animancer
        animancer.Play(animationName);
    }
    else
    {
        // Play animation using Animator
        animator.Play(animationName);
    }
}

public void PlayAnimation(string animationName, float crossFadeDuration)
{
    if (m_useAnimancer)
    {
        // Play animation using Animancer with crossfade
        animancer.Play(animationName, crossFadeDuration);
    }
    else
    {
        // Play animation using Animator with crossfade
        animator.CrossFade(animationName, crossFadeDuration);
    }
}

public void PlayAnimationState(Enum animationNameState, float crossFadeDuration, Object conditions = null)
{
    if (States.ContainsKey(animationNameState))
    {
        StateMachine.TrySetState(States[animationNameState], crossFadeDuration, conditions);
    }
}
```

### Setting Animation Parameters

```csharp
public void SetFloat(string parameterName, float value, float dampTime, float deltaTime)
{
    if (m_useAnimancer)
    {
        // Set float parameter using Animancer
        // Implementation depends on Animancer setup
    }
    else
    {
        // Set float parameter using Animator
        animator.SetFloat(parameterName, value, dampTime, deltaTime);
    }
}

public void SetBool(string parameterName, bool value)
{
    if (m_useAnimancer)
    {
        // Set bool parameter using Animancer
        // Implementation depends on Animancer setup
    }
    else
    {
        // Set bool parameter using Animator
        animator.SetBool(parameterName, value);
    }
}

public void SetTrigger(string parameterName)
{
    if (m_useAnimancer)
    {
        // Set trigger using Animancer
        // Implementation depends on Animancer setup
    }
    else
    {
        // Set trigger using Animator
        animator.SetTrigger(parameterName);
    }
}
```

## Integration with Other Modules

### Movement Module

The `MovementModule` communicates with the `AnimationModule` to play appropriate animations based on movement state:

```csharp
// In MovementModule
if (SubState == MovementSubState.WalkingWithTurn)
{
    if (m_useAnimancer)
        animationModule.PlayAnimationState(MovementSubState.WalkingWithTurn, 0.25f);
    else
    {
        animationModule.SetFloat("InputMagnitude", characterParameters.InputMagnitude.Value, 0.15f, Time.deltaTime);
        animationModule.SetFloat("SprintFactor", 0, 0.15f, Time.deltaTime);
    }
}
```

### Aiming Module

The `AimingModule` communicates with the `AnimationModule` to play appropriate animations during aiming:

```csharp
// In AimingModule
public void StartAiming()
{
    ragdollAiming.enabled = true;
    ragdollAiming.weight = 1;
    
    // Notify animation module of aiming state
    EventManager.Broadcast(new OnAimingStateChangeEvent(true));
}
```

## Best Practices

1. **Use State-Based Animations**: Organize animations around clear states
2. **Apply Appropriate Crossfading**: Use crossfading for smooth transitions
3. **Separate Upper and Lower Body Animations**: Use animation layers for independent control
4. **Use Animation Events**: Trigger game events at specific animation points
5. **Optimize Animation Blending**: Minimize the number of active blends

## Common Issues and Solutions

### Animations Don't Transition Smoothly

- Check crossfade duration values
- Verify animation state transitions
- Adjust animation blending parameters

### Animations Don't Match Movement

- Ensure proper communication with the `MovementModule`
- Verify animation parameter updates
- Check animation transition times

### Upper Body Animations Conflict with Lower Body

- Verify layer weights and masks
- Check animation priorities
- Adjust blending between layers
