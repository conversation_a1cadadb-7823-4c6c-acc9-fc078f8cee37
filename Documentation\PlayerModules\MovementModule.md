# Movement Module Documentation

## Overview

The `MovementModule` is responsible for controlling the player character's movement and rotation. It processes input from the `InputModule` and applies appropriate movement and rotation to the character based on the current state.

## Features

- Handles different movement states (standing, walking, etc.)
- Controls character rotation
- Manages movement transitions
- Communicates with the `AnimationModule` for movement animations
- Supports different movement modes (normal, aiming)

## Implementation

The `MovementModule` implements the `IModule<MovementSubState>` interface and the `IUpdateSubModule<MovementSubState>` interface for state management.

```csharp
public class MovementModule : MonoBehaviour, IModule<MovementSubState>, IUpdateSubModule<MovementSubState>,
    IUpdateState<AnimationSubState>
{
    [SerializeField] private MovementModuleConfiguration movementConfig;
    
    [field: SerializeField] public int ControllerIndex { get; private set; }
    [field: SerializeField] public List<MainState> MainState { get; private set; }
    
    [SerializeField] private MovementSubState _moduleSubState;
    
    public MovementSubState SubState
    {
        get { return _moduleSubState; }
        set
        {
            _moduleSubState = value;
            PlayerController.Instance.StateManager.CurrentSubState = _moduleSubState;
        }
    }
    
    // Movement methods
    // ...
}
```

## Movement States

The `MovementModule` manages several movement states:

- **Standing**: The character is stationary
- **WalkingStart**: The character is starting to walk
- **WalkingWithTurn**: The character is walking and turning
- **Stop**: The character is stopping
- **InPositionRotation**: The character is rotating in place

### State Transitions

```csharp
if (characterParameters.InputMagnitude.Value > 0.2f && !IsMoving && SubState == MovementSubState.Standing)
{
    if (!m_useAnimancer)
        SetWalkStartAngle();

    IsMoving = true;

    CancelPreviousTask("Standing");
    _cancellationTokenSource = new CancellationTokenSource();
    _stateName = "Standing";
    this.WaitForSecondsAsync("Standing", 0.04f,
        () => { SubState = MovementSubState.WalkingStart; }
        , _cancellationTokenSource.Token);
}
```

## Movement Processing

The module processes movement in several steps:

1. **Input Processing**: Processes input from the `InputModule`
2. **State Determination**: Determines the appropriate movement state
3. **Rotation Calculation**: Calculates rotation based on input and current state
4. **Movement Application**: Applies movement to the character
5. **Animation Notification**: Notifies the `AnimationModule` of movement changes

### Movement Application

```csharp
private void ApplyMovement(float horizontal, float vertical)
{
    // Calculate movement direction
    Vector3 moveDirection = new Vector3(horizontal, 0, vertical).normalized;
    
    // Apply movement based on current state
    if (SubState == MovementSubState.WalkingWithTurn || SubState == MovementSubState.WalkingStart)
    {
        // Calculate target velocity
        Vector3 targetVelocity = moveDirection * movementConfig.moveSpeed;
        
        // Apply smoothing
        currentVelocity = Vector3.SmoothDamp(
            currentVelocity, 
            targetVelocity, 
            ref velocityChangeSpeed, 
            movementConfig.accelerationTime
        );
        
        // Apply movement
        transform.position += currentVelocity * Time.deltaTime;
    }
}
```

## Character Rotation

The module handles character rotation based on input direction and current state:

```csharp
private void UpdateCharacterRotation()
{
    if (characterParameters.IsAiming && characterParameters.HasAimTarget)
    {
        // Rotate towards aim target
        Vector3 targetDirection = characterParameters.AimTarget - transform.position;
        targetDirection.y = 0;
        
        Quaternion targetRotation = Quaternion.LookRotation(targetDirection);
        transform.rotation = Quaternion.Slerp(
            transform.rotation, 
            targetRotation, 
            movementConfig.rotationSpeed * Time.deltaTime
        );
    }
    else
    {
        // Rotate based on input direction
        if (characterParameters.InputMagnitude.Value > 0.1f)
        {
            Vector3 inputDirection = new Vector3(
                characterParameters.Horizontal.Value, 
                0, 
                characterParameters.Vertical.Value
            ).normalized;
            
            if (inputDirection != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(inputDirection);
                transform.rotation = Quaternion.Slerp(
                    transform.rotation, 
                    targetRotation, 
                    movementConfig.rotationSpeed * Time.deltaTime
                );
            }
        }
    }
}
```

## Special Movement Features

### Force Stop and Stand

```csharp
public void ForceStopAndStand()
{
    DebugLogManager.Instance.Log("[MovementModule] Forcing Stop -> Standing due to target loss.", DebugLogSettings.LogType.PlayerMovement);
    IsMoving = false;
    SubState = MovementSubState.Stop;
    ToStop();
    // After stop, transition to standing
    StartCoroutine(WaitAndSetStanding());
}

private IEnumerator WaitAndSetStanding()
{
    yield return new WaitForSeconds(0.15f);
    SubState = MovementSubState.Standing;
    DebugLogManager.Instance.Log("[MovementModule] Transitioned to Standing after Stop.", DebugLogSettings.LogType.PlayerMovement);
}
```

## Configuration

The `MovementModule` is configured through the `MovementModuleConfiguration` ScriptableObject:

```csharp
[CreateAssetMenu(fileName = "MovementConfiguration", menuName = "Configuration/MovementConfiguration")]
public class MovementModuleConfiguration : ScriptableObject
{
    [Header("Movement Improvement")]
    [Tooltip("Enable improved movement for smoother direction changes")]
    public bool improvedMovement = false;
    
    [Tooltip("Use root motion for turning animations")]
    public bool useRootMotionForTurning = false;

    [Header("Movement Parameters")]
    [Tooltip("Time to reach full speed")]
    public float accelerationTime = 0.1f;
    
    [Tooltip("Base movement speed")]
    public float moveSpeed = 5f;
    
    [Tooltip("Rotation speed in degrees per second")]
    public float rotationSpeed = 10f;
    
    // Additional configuration parameters
    // ...
}
```

## Integration with Other Modules

### Animation Module

The `MovementModule` communicates with the `AnimationModule` to play appropriate animations based on movement state:

```csharp
if (SubState == MovementSubState.WalkingWithTurn)
{
    if (m_useAnimancer)
        animationModule.PlayAnimationState(MovementSubState.WalkingWithTurn, 0.25f);
    else
    {
        animationModule.SetFloat("InputMagnitude", characterParameters.InputMagnitude.Value, 0.15f, Time.deltaTime);
        animationModule.SetFloat("SprintFactor", 0, 0.15f, Time.deltaTime);
    }
}
```

### Aiming Module

The `MovementModule` receives aim target information from the `AimingModule` to control character rotation during aiming:

```csharp
if (characterParameters.IsAiming && characterParameters.HasAimTarget)
{
    // Rotate towards aim target
    // ...
}
```

## Best Practices

1. **Use State-Based Movement**: Organize movement logic around clear states
2. **Apply Appropriate Smoothing**: Use smoothing for natural movement transitions
3. **Separate Movement and Rotation**: Handle movement and rotation separately
4. **Use Configuration ScriptableObjects**: Keep movement parameters configurable
5. **Handle Edge Cases**: Implement special handling for edge cases like target loss

## Common Issues and Solutions

### Character Movement Is Jerky

- Increase smoothing parameters
- Check for frame rate issues
- Verify input processing

### Character Rotation Is Incorrect

- Check rotation calculation logic
- Verify target direction normalization
- Adjust rotation speed

### Animation Doesn't Match Movement

- Ensure proper communication with the `AnimationModule`
- Verify animation parameter updates
- Check animation transition times
