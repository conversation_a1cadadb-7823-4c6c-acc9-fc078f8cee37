# ECS Technical Documentation - Part 7: Spatial Partitioning and Optimization

## Introduction

This document details the spatial partitioning and optimization systems used in the project. These systems are responsible for efficiently finding nearby entities, optimizing spatial queries, and improving overall performance with large numbers of entities.

## Table of Contents

1. [Spatial Partitioning Components](#spatial-partitioning-components)
2. [KD-Tree System](#kd-tree-system)
3. [Spatial Hash Grid System](#spatial-hash-grid-system)
4. [Performance Optimization](#performance-optimization)
5. [Implementation Guidelines](#implementation-guidelines)

## Spatial Partitioning Components

### KD-Tree Components

```csharp
// KD-Tree component
public struct KDTreeComponent : IComponentData
{
    public Native3DKDTree<GimmeDOTSGeometry.Position3D> Tree;
    public float SearchRadius;
    public bool IsInitialized;
}

// KD-Tree search result buffer
public struct KDTreeSearchResult : IBufferElementData
{
    public Entity Entity;
    public float3 Position;
    public float Distance;
}
```

### Spatial Hash Grid Components

```csharp
// Spatial hash grid component
public struct SpatialHashGridComponent : IComponentData
{
    public float CellSize;
    public int GridSize;  // Single size for both X and Z
    public float3 GridOrigin;
}

// Grid cell buffer element
public struct GridCell : IBufferElementData, IPosition3D, IEquatable<GridCell>
{
    public Entity Entity;
    public int GridX;
    public int GridZ;
    public float3 Position { get; set; }

    public float3 GetPosition() => Position;

    public bool Equals(GridCell other)
    {
        return Entity == other.Entity && 
               Position.Equals(other.Position) && 
               GridX == other.GridX && 
               GridZ == other.GridZ;
    }

    public override int GetHashCode()
    {
        return System.HashCode.Combine(Entity, Position, GridX, GridZ);
    }
}
```

## KD-Tree System

The KD-Tree is a space-partitioning data structure that organizes points in a k-dimensional space. It's particularly efficient for range searches and nearest neighbor queries.

### KDTreeSystem

```csharp
[BurstCompile]
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateBefore(typeof(SpatialHashGridSystem))]
public partial struct KDTreeSystem : ISystem
{
    private EntityQuery m_EnemyQuery;
    private EntityQuery m_TreeQuery;
    private EntityQuery m_SensorDetector;

    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<SphereDetectSensorComponent>();
        var builder = new EntityQueryBuilder(Allocator.Temp)
            .WithAll<EnemyTag, LocalToWorld>();
        m_EnemyQuery = state.GetEntityQuery(builder);

        builder = new EntityQueryBuilder(Allocator.Temp)
            .WithAll<KDTreeComponent>();
        m_TreeQuery = state.GetEntityQuery(builder);

        if (m_TreeQuery.IsEmpty)
        {
            CreateKDTreeEntity(ref state);
        }
    }

    private void CreateKDTreeEntity(ref SystemState state)
    {
        var treeEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponent<KDTreeComponent>(treeEntity);
        state.EntityManager.AddBuffer<KDTreeSearchResult>(treeEntity);
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        if (m_EnemyQuery.IsEmpty)
            return;

        var sensor = state.EntityManager.GetComponentData<SphereDetectSensorComponent>(SystemAPI.GetSingletonEntity<SphereDetectSensorComponent>());
        
        // Only update if KDTree is enabled
        if (!sensor.UseKDTree)
            return;

        var treeEntity = m_TreeQuery.GetSingletonEntity();
        var treeComponent = state.EntityManager.GetComponentData<KDTreeComponent>(treeEntity);
        var searchResults = state.EntityManager.GetBuffer<KDTreeSearchResult>(treeEntity);
    
        var enemies = m_EnemyQuery.ToEntityArray(Allocator.TempJob);
        var positions = m_EnemyQuery.ToComponentDataArray<LocalToWorld>(Allocator.TempJob);

        // Create or update the KD-Tree
        if (!treeComponent.IsInitialized)
        {
            // Create a new KD-Tree
            var points = new NativeArray<Position3D>(enemies.Length, Allocator.TempJob);
            
            for (int i = 0; i < enemies.Length; i++)
            {
                points[i] = new Position3D { Position = positions[i].Position };
            }
            
            treeComponent.Tree = new Native3DKDTree<Position3D>(points, Allocator.Persistent);
            treeComponent.IsInitialized = true;
            treeComponent.SearchRadius = sensor.DetectionRadius;
            
            points.Dispose();
        }
        else
        {
            // Update the existing KD-Tree
            var points = new NativeArray<Position3D>(enemies.Length, Allocator.TempJob);
            
            for (int i = 0; i < enemies.Length; i++)
            {
                points[i] = new Position3D { Position = positions[i].Position };
            }
            
            if (treeComponent.Tree.IsCreated)
            {
                treeComponent.Tree.Dispose();
            }
            
            treeComponent.Tree = new Native3DKDTree<Position3D>(points, Allocator.Persistent);
            treeComponent.SearchRadius = sensor.DetectionRadius;
            
            points.Dispose();
        }
        
        // Update the component
        state.EntityManager.SetComponentData(treeEntity, treeComponent);
        
        // Clean up
        enemies.Dispose();
        positions.Dispose();
    }
}
```

### Using the KD-Tree for Queries

```csharp
// Example of using the KD-Tree for radius queries
public void FindNearbyEnemies(float3 position, float radius, NativeList<Entity> results)
{
    if (kdTreeQuery.IsEmpty)
        return;
        
    var kdTreeEntity = kdTreeQuery.GetSingletonEntity();
    var kdTree = EntityManager.GetComponentData<KDTreeComponent>(kdTreeEntity);
    
    if (!kdTree.Tree.IsCreated)
        return;
        
    // Create a position for the query
    var queryPosition = new Position3D { Position = position };
    
    // Perform radius search
    var nearbyPoints = new NativeList<Position3D>(Allocator.Temp);
    kdTree.Tree.RadiusSearch(queryPosition, radius, nearbyPoints);
    
    // Get the search results buffer
    var searchResults = EntityManager.GetBuffer<KDTreeSearchResult>(kdTreeEntity);
    searchResults.Clear();
    
    // Convert points to entities
    for (int i = 0; i < nearbyPoints.Length; i++)
    {
        var point = nearbyPoints[i];
        var index = kdTree.Tree.GetPointIndex(point);
        
        if (index >= 0 && index < enemyEntities.Length)
        {
            var entity = enemyEntities[index];
            var distance = math.distance(position, point.Position);
            
            // Add to results
            results.Add(entity);
            
            // Add to search results buffer
            searchResults.Add(new KDTreeSearchResult
            {
                Entity = entity,
                Position = point.Position,
                Distance = distance
            });
        }
    }
    
    nearbyPoints.Dispose();
}
```

## Spatial Hash Grid System

The Spatial Hash Grid is a grid-based spatial partitioning system that divides the world into cells and assigns entities to cells based on their position. It's particularly efficient for uniform distributions of entities.

### SpatialHashGridSystem

```csharp
[BurstCompile]
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial struct SpatialHashGridSystem : ISystem
{
    private EntityQuery m_EnemyQuery;
    private EntityQuery m_GridQuery;
    private const float DEFAULT_CELL_SIZE = 2f;
    private const int GRID_SIZE = 100;

    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        var builder = new EntityQueryBuilder(Allocator.Temp)
            .WithAll<EnemyTag, LocalToWorld>();
        m_EnemyQuery = state.GetEntityQuery(builder);
        
        builder = new EntityQueryBuilder(Allocator.Temp)
            .WithAll<SpatialHashGridComponent, GridCell>();
        m_GridQuery = state.GetEntityQuery(builder);

        if (m_GridQuery.IsEmpty)
        {
            CreateGridEntity(ref state);
        }

        Debug.Log("[SpatialHashGridSystem] System created");
    }

    private void CreateGridEntity(ref SystemState state)
    {
        var gridEntity = state.EntityManager.CreateEntity();
        var buffer = state.EntityManager.AddBuffer<GridCell>(gridEntity);
        state.EntityManager.AddComponentData(gridEntity, new SpatialHashGridComponent
        {
            CellSize = DEFAULT_CELL_SIZE,
            GridSize = GRID_SIZE
        });
        Debug.Log("[SpatialHashGridSystem] Created new grid entity");
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        if (m_EnemyQuery.IsEmpty)
            return;

        var gridEntity = m_GridQuery.GetSingletonEntity();
        var grid = state.EntityManager.GetComponentData<SpatialHashGridComponent>(gridEntity);
        var cells = state.EntityManager.GetBuffer<GridCell>(gridEntity);

        cells.Clear();

        float gridWorldSize = grid.GridSize * grid.CellSize;
        float halfGridSize = gridWorldSize / 2f;

        var enemies = m_EnemyQuery.ToEntityArray(Allocator.TempJob);
        var positions = m_EnemyQuery.ToComponentDataArray<LocalToWorld>(Allocator.TempJob);

        // Create a temporary buffer to store grid cells
        var tempCells = new NativeArray<GridCell>(enemies.Length, Allocator.TempJob);

        // Assign entities to grid cells
        for (int i = 0; i < enemies.Length; i++)
        {
            var entity = enemies[i];
            var position = positions[i].Position;
            
            // Calculate grid coordinates
            int gridX = (int)math.floor((position.x + halfGridSize) / grid.CellSize);
            int gridZ = (int)math.floor((position.z + halfGridSize) / grid.CellSize);
            
            // Clamp to grid bounds
            gridX = math.clamp(gridX, 0, grid.GridSize - 1);
            gridZ = math.clamp(gridZ, 0, grid.GridSize - 1);
            
            // Create grid cell
            var cell = new GridCell
            {
                Entity = entity,
                Position = position,
                GridX = gridX,
                GridZ = gridZ
            };
            
            tempCells[i] = cell;
        }
        
        // Add cells to buffer
        for (int i = 0; i < tempCells.Length; i++)
        {
            cells.Add(tempCells[i]);
        }
        
        // Clean up
        enemies.Dispose();
        positions.Dispose();
        tempCells.Dispose();
    }
}
```

### Using the Spatial Hash Grid for Queries

```csharp
// Example of using the Spatial Hash Grid for radius queries
public void FindNearbyEntities(float3 position, float radius, NativeList<Entity> results)
{
    if (gridQuery.IsEmpty)
        return;
        
    var gridEntity = gridQuery.GetSingletonEntity();
    var grid = EntityManager.GetComponentData<SpatialHashGridComponent>(gridEntity);
    var cells = EntityManager.GetBuffer<GridCell>(gridEntity);
    
    float gridWorldSize = grid.GridSize * grid.CellSize;
    float halfGridSize = gridWorldSize / 2f;
    
    // Calculate grid coordinates for the query position
    int centerGridX = (int)math.floor((position.x + halfGridSize) / grid.CellSize);
    int centerGridZ = (int)math.floor((position.z + halfGridSize) / grid.CellSize);
    
    // Calculate cell radius
    int cellRadius = (int)math.ceil(radius / grid.CellSize);
    
    // Iterate through nearby cells
    for (int x = centerGridX - cellRadius; x <= centerGridX + cellRadius; x++)
    {
        for (int z = centerGridZ - cellRadius; z <= centerGridZ + cellRadius; z++)
        {
            // Skip cells outside the grid
            if (x < 0 || x >= grid.GridSize || z < 0 || z >= grid.GridSize)
                continue;
                
            // Check entities in this cell
            for (int i = 0; i < cells.Length; i++)
            {
                var cell = cells[i];
                
                if (cell.GridX == x && cell.GridZ == z)
                {
                    // Check distance
                    float distance = math.distance(position, cell.Position);
                    
                    if (distance <= radius)
                    {
                        results.Add(cell.Entity);
                    }
                }
            }
        }
    }
}
```

## Performance Optimization

### Optimizing Spatial Queries

To optimize spatial queries, the project uses several techniques:

1. **Query Frequency Limitation**:
   ```csharp
   // Only update detection at specified intervals
   float currentTime = Time.ElapsedTime;
   if (currentTime - sensor.LastCheckTime < sensor.CheckInterval)
       return;
   ```

2. **Frustum Culling**:
   ```csharp
   // Only process entities within the camera frustum
   var camera = Camera.main;
   var planes = GeometryUtility.CalculateFrustumPlanes(camera);
   
   foreach (var entity in entities)
   {
       var position = EntityManager.GetComponentData<LocalToWorld>(entity).Position;
       var bounds = new Bounds(position, Vector3.one);
       
       if (GeometryUtility.TestPlanesAABB(planes, bounds))
       {
           // Entity is visible, process it
       }
   }
   ```

3. **Distance-Based LOD**:
   ```csharp
   // Apply different levels of detail based on distance
   float distanceToCamera = math.distance(position, cameraPosition);
   
   if (distanceToCamera < 10f)
   {
       // High detail
   }
   else if (distanceToCamera < 30f)
   {
       // Medium detail
   }
   else
   {
       // Low detail
   }
   ```

### Optimizing Entity Processing

To optimize entity processing, the project uses several techniques:

1. **Burst Compilation**:
   ```csharp
   [BurstCompile]
   private partial struct ProcessEntitiesJob : IJobEntity
   {
       public void Execute(Entity entity, ref LocalTransform transform)
       {
           // Burst-compiled code runs much faster
       }
   }
   ```

2. **Job System**:
   ```csharp
   // Process entities in parallel
   var job = new ProcessEntitiesJob();
   var handle = job.ScheduleParallel(Dependency);
   Dependency = handle;
   ```

3. **Entity Archetype Optimization**:
   ```csharp
   // Group entities with similar component sets for better cache utilization
   var archetype = EntityManager.CreateArchetype(
       typeof(LocalToWorld),
       typeof(EnemyTag),
       typeof(HealthComponent)
   );
   
   var entities = new NativeArray<Entity>(100, Allocator.Temp);
   EntityManager.CreateEntity(archetype, entities);
   ```

4. **Component Data Optimization**:
   ```csharp
   // Use blittable types for better performance
   public struct OptimizedComponent : IComponentData
   {
       public float Value;
       public int IntValue;
       public float3 Position;
   }
   
   // Instead of
   public struct UnoptimizedComponent : IComponentData
   {
       public string Text; // Managed type, not blittable
       public List<float> Values; // Managed type, not blittable
   }
   ```

## Implementation Guidelines

When implementing or modifying spatial partitioning and optimization systems, follow these guidelines:

1. **Spatial Partitioning Selection**:
   - Use KD-Tree for non-uniform distributions and nearest neighbor queries
   - Use Spatial Hash Grid for uniform distributions and radius queries
   - Consider using both systems for different query types
   - Benchmark different approaches for your specific use case

2. **Query Optimization**:
   - Limit query frequency based on distance and importance
   - Use frustum culling to only process visible entities
   - Implement distance-based level of detail
   - Cache query results when appropriate

3. **Performance Considerations**:
   - Use Burst compilation for performance-critical code
   - Implement parallel processing with the Job System
   - Optimize entity archetypes for better cache utilization
   - Use blittable types for component data

4. **Memory Management**:
   - Dispose native containers when no longer needed
   - Use temporary allocators for short-lived containers
   - Implement object pooling for frequently created/destroyed objects
   - Monitor memory usage and optimize as needed

5. **Debugging and Profiling**:
   - Add visualization tools for spatial partitioning
   - Implement performance monitoring and logging
   - Use Unity Profiler to identify bottlenecks
   - Add debug modes for spatial query visualization

6. **Integration with Other Systems**:
   - Coordinate with the detection system for efficient enemy finding
   - Integrate with the spawning system for spatial distribution
   - Connect with the animation system for LOD-based animation
   - Link with the rendering system for culling and LOD
