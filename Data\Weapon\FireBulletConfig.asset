%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 546374d2430cb1e4aaa47c3441db500a, type: 3}
  m_Name: FireBulletConfig
  m_EditorClassIdentifier: 
  bulletName: Fire Bullet
  bulletType: 1
  bulletPrefab: {fileID: 0}
  baseStats:
    speed: 18
    damageMultiplier: 1.3
    maxDistance: 80
    canPenetrate: 0
    maxPenetrations: 0
    penetrationDamageReduction: 0.3
    explodeOnImpact: 0
    explosionRadius: 0
    explosionDamageMultiplier: 0.7
    impactEffectPrefab: {fileID: 0}
    trailEffectPrefab: {fileID: 0}
  upgradeLevels:
  - levelName: Enhanced Fire Bullet
    upgradeCost: 150
    stats:
      speed: 19
      damageMultiplier: 1.5
      maxDistance: 85
      canPenetrate: 0
      maxPenetrations: 0
      penetrationDamageReduction: 0.3
      explodeOnImpact: 0
      explosionRadius: 0
      explosionDamageMultiplier: 0.7
      impactEffectPrefab: {fileID: 0}
      trailEffectPrefab: {fileID: 0}
  - levelName: Advanced Fire Bullet
    upgradeCost: 300
    stats:
      speed: 20
      damageMultiplier: 1.8
      maxDistance: 90
      canPenetrate: 0
      maxPenetrations: 0
      penetrationDamageReduction: 0.3
      explodeOnImpact: 0
      explosionRadius: 0
      explosionDamageMultiplier: 0.7
      impactEffectPrefab: {fileID: 0}
      trailEffectPrefab: {fileID: 0}
