# Target Selection System Guide

This document explains how the target selection system works and how to tune it for optimal gameplay experience.

## Overview

The target selection system determines which enemy the player should aim at when multiple enemies are present. It uses a combination of techniques:

1. **TriadCluster Algorithm**: Groups nearby enemies into clusters and selects the best cluster
2. **Target Stability**: Prevents rapid switching between targets when enemies are close to each other
3. **Adaptive Smoothing**: Smoothly transitions between targets based on distance

## TriadCluster Settings

The TriadCluster algorithm is configured through the `TriadClusterSettingsComponent`. Here's what each setting does:

| Setting | Description | Recommended Value | Effect |
|---------|-------------|-------------------|--------|
| `EnableClustering` | Turns clustering on/off | `true` | When enabled, nearby enemies are grouped into clusters |
| `ClusteringRadius` | Maximum distance between enemies to be considered part of the same cluster | `15.0f` | Smaller values create tighter clusters, larger values group more enemies together |
| `ClusterScoreWeight_CentroidDistance` | How much to favor clusters closer to the player | `-0.5f` | More negative values favor closer clusters more strongly |
| `ClusterScoreWeight_MemberCount` | How much to favor clusters with more enemies | `0.5f` | Higher values favor larger groups of enemies |
| `ClusterScoreWeight_AverageScore` | How much to favor clusters with higher-scoring enemies | `0.4f` | Higher values favor clusters with more important enemies |
| `TargetSelectionMode` | How to choose a target within a cluster | `HighestScore` | Options: `HighestScore`, `ClosestToCentroid`, `ClosestToPlayer` |
| `MaxSimultaneousTargets` | Maximum number of targets to select | `1` | Usually 1, can be increased for multi-targeting abilities |
| `ClusterHysteresisThreshold` | How much better a new cluster must be to switch | `0.5f` | Higher values make targeting more stable |

## AimingModule Settings

The `AimingModule` component controls how targets are selected and tracked:

| Setting | Description | Recommended Value | Effect |
|---------|-------------|-------------------|--------|
| `improvedTargetSelection` | Enables enhanced target selection | `true` | Activates the improved target selection algorithm |
| `targetStickiness` | How much to favor the current target | `1.5f` | Higher values make it harder to switch targets |
| `minTargetSwitchTime` | Minimum time to keep the same target | `1.0f` | Higher values prevent rapid target switching |
| `targetPositionSmoothingFactor` | How smoothly to transition between targets | `3.0f` | Lower values make transitions smoother but slower |
| `minScoreDifferenceToSwitch` | Score improvement needed to switch targets | `0.4f` | Higher values require new targets to be significantly better |
| `targetStabilityThreshold` | Time a potential target must be valid before switching | `0.5f` | Higher values require more consistent targeting |
| `clusterPreference` | How much to favor targets in clusters | `0.5f` | Higher values prefer targets that are grouped with others |

## Tuning for Different Scenarios

### For Fast-Paced Combat

```
ClusteringRadius: 10.0f
ClusterScoreWeight_CentroidDistance: -0.7f
ClusterScoreWeight_MemberCount: 0.3f
minTargetSwitchTime: 0.7f
targetStabilityThreshold: 0.3f
```

### For Strategic Combat

```
ClusteringRadius: 20.0f
ClusterScoreWeight_AverageScore: 0.6f
ClusterHysteresisThreshold: 0.7f
minTargetSwitchTime: 1.5f
targetStabilityThreshold: 0.8f
```

### For Enemies That Are Very Close Together

```
ClusteringRadius: 8.0f
ClusterScoreWeight_CentroidDistance: -0.4f
ClusterScoreWeight_MemberCount: 0.4f
ClusterScoreWeight_AverageScore: 0.6f
minScoreDifferenceToSwitch: 0.6f
targetStabilityThreshold: 0.7f
```

## Troubleshooting

### Rapid Target Switching

If the player's aim is rapidly switching between targets:

1. Increase `minTargetSwitchTime` to enforce a longer cooldown
2. Increase `targetStabilityThreshold` to require more consistent targeting
3. Increase `minScoreDifferenceToSwitch` to require new targets to be significantly better
4. Decrease `ClusteringRadius` to create tighter clusters

### Difficulty Targeting Specific Enemies

If players struggle to target specific enemies:

1. Increase `ClusterScoreWeight_AverageScore` to favor important targets
2. Decrease `targetStickiness` to make it easier to switch targets
3. Set `TargetSelectionMode` to `HighestScore` to prioritize important enemies

### Sluggish Target Transitions

If target transitions feel too slow:

1. Increase `targetPositionSmoothingFactor` for faster transitions
2. Decrease `targetStabilityThreshold` for quicker target acquisition

## Best Practices

1. **Test with different enemy densities**: Settings that work well for sparse enemies may not work for dense groups
2. **Balance stability and responsiveness**: Too much stability makes targeting feel unresponsive, too little makes it erratic
3. **Consider player skill level**: Less experienced players benefit from more stability, while expert players may prefer responsiveness
4. **Adjust for game pace**: Fast-paced games need quicker targeting, strategic games benefit from more deliberate targeting
