# ECS Technical Documentation - Part 6: Enemy Spawning and AI

## Introduction

This document details the enemy spawning and AI systems used in the project. These systems are responsible for creating enemies, managing their lifecycle, controlling their behavior, and optimizing performance with large numbers of entities.

## Table of Contents

1. [Spawning Components](#spawning-components)
2. [Spawning Systems](#spawning-systems)
3. [Navigation and Movement](#navigation-and-movement)
4. [AI Behavior](#ai-behavior)
5. [Performance Optimization](#performance-optimization)
6. [Implementation Guidelines](#implementation-guidelines)

## Spawning Components

### Core Spawning Components

```csharp
// Spawn component for tracking when an entity was spawned
public struct SpawnComponent : IComponentData
{
    public float SpawnTime;    // Time when the entity was spawned
}

// Enemy spawn component
public struct EnemySpawnComponent : IComponentData
{
    public Entity enemyPrefab;
    public float spawnInterval;
    public float spawnRadius;
    public float playerAvoidRadius;
    public int spawnBatchSize;
    public int maxEnemyCount; // Maximum number of enemies allowed
    public int currentEnemyCount; // Current number of spawned enemies
    public float timeSinceLastSpawn;
}

// Crowds navigation spawner
public struct Spawner : IComponentData
{
    public Entity Group;
    public Entity Prefab;
    public float Interval;
    public int Batch;
    public float3 Size;
    public int Count;
    public int MaxCount;
    public Unity.Mathematics.Random Random;
    public float Elapsed;
    public float3 Destination;
    public bool ContinuousSpawning;
}

// Character spawner for GPU ECS Animation
public struct CharacterSpawner : IComponentData
{
    public Entity Prefab;
    public Entity Group;
    public float Interval;
    public int Batch;
    public float3 Size;
    public int Count;
    public int MaxCount;
    public float3 Destination;
    public Unity.Mathematics.Random Random;
    public float Elapsed;
}
```

## Spawning Systems

### OptimizedSpawnerSystem

The `OptimizedSpawnerSystem` is a legacy system that handles spawning without Crowds navigation.

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class OptimizedSpawnerSystem : SystemBase
{
    private EntityQuery playerQuery;
    private EntityQuery enemyQuery;
    
    protected override void OnCreate()
    {
        playerQuery = GetEntityQuery(ComponentType.ReadOnly<PlayerTag>(), ComponentType.ReadOnly<LocalToWorld>());
        enemyQuery = GetEntityQuery(ComponentType.ReadOnly<EnemyTag>(), ComponentType.Exclude<DeadTag>());
    }
    
    protected override void OnUpdate()
    {
        if (playerQuery.IsEmpty)
            return;
            
        var playerEntity = playerQuery.GetSingletonEntity();
        var playerTransform = EntityManager.GetComponentData<LocalToWorld>(playerEntity);
        var playerPosition = playerTransform.Position;
        
        // Process each spawner
        Entities.ForEach((Entity entity, ref EnemySpawnComponent spawner) =>
        {
            // Update timer
            spawner.timeSinceLastSpawn += Time.DeltaTime;
            
            // Check if it's time to spawn
            if (spawner.timeSinceLastSpawn >= spawner.spawnInterval)
            {
                spawner.timeSinceLastSpawn = 0f;
                
                // Get current enemy count
                int currentEnemyCount = enemyQuery.CalculateEntityCount();
                spawner.currentEnemyCount = currentEnemyCount;
                
                // Check if we can spawn more enemies
                if (currentEnemyCount < spawner.maxEnemyCount)
                {
                    // Calculate how many to spawn
                    int spawnCount = math.min(spawner.spawnBatchSize, spawner.maxEnemyCount - currentEnemyCount);
                    
                    // Spawn enemies
                    for (int i = 0; i < spawnCount; i++)
                    {
                        // Generate random position
                        float angle = UnityEngine.Random.Range(0f, 2f * math.PI);
                        float distance = UnityEngine.Random.Range(spawner.playerAvoidRadius, spawner.spawnRadius);
                        float3 offset = new float3(
                            math.cos(angle) * distance,
                            0f,
                            math.sin(angle) * distance
                        );
                        float3 spawnPosition = playerPosition + offset;
                        
                        // Instantiate enemy
                        Entity enemy = EntityManager.Instantiate(spawner.enemyPrefab);
                        
                        // Set position
                        EntityManager.SetComponentData(enemy, new Translation { Value = spawnPosition });
                        
                        // Add spawn component
                        EntityManager.AddComponentData(enemy, new SpawnComponent { SpawnTime = (float)Time.ElapsedTime });
                    }
                }
            }
        }).WithoutBurst().Run(); // Can't use Burst with EntityManager.Instantiate
    }
}
```

### SpawnerSystem

The `SpawnerSystem` is a more advanced system that uses Crowds navigation for enemy movement.

```csharp
[RequireMatchingQueriesForUpdate]
[UpdateInGroup(typeof(FixedStepSimulationSystemGroup))]
[UpdateBefore(typeof(AgentSystemGroup))]
public partial struct SpawnerSystem : ISystem
{
    public void OnUpdate(ref SystemState state)
    {
        var ecb = GetSingleton<EndInitializationEntityCommandBufferSystem.Singleton>();
        
        // Schedule the spawner job
        new SpawnerJob
        {
            Ecb = ecb.CreateCommandBuffer(state.WorldUnmanaged),
            DeltaTime = state.WorldUnmanaged.Time.DeltaTime,
        }.Schedule();
    }
    
    private partial struct SpawnerJob : IJobEntity
    {
        public EntityCommandBuffer Ecb;
        public float DeltaTime;
        
        private void Execute(ref Spawner spawner, in LocalTransform transform)
        {
            // Update elapsed time
            spawner.Elapsed += DeltaTime;
            
            // Check if it's time to spawn
            if (spawner.Elapsed >= spawner.Interval)
            {
                spawner.Elapsed = 0;
                
                // Check if we can spawn more entities
                if (spawner.Count < spawner.MaxCount)
                {
                    // Calculate how many to spawn
                    int spawnCount = math.min(spawner.Batch, spawner.MaxCount - spawner.Count);
                    
                    // Spawn entities
                    for (int i = 0; i < spawnCount; i++)
                    {
                        // Generate random position
                        float3 offset = spawner.Random.NextFloat3(-spawner.Size, spawner.Size);
                        float3 position = transform.Position + offset;
                        
                        // Instantiate entity
                        Entity entity = Ecb.Instantiate(spawner.Prefab);
                        
                        // Set position
                        Ecb.SetComponent(entity, new LocalTransform
                        {
                            Position = position,
                            Rotation = quaternion.identity,
                            Scale = 1
                        });
                        
                        // Set destination
                        Ecb.SetComponent(entity, new AgentBody
                        {
                            Destination = spawner.Destination,
                            IsStopped = false
                        });
                        
                        // Set crowd group
                        Ecb.SetSharedComponent(entity, new AgentCrowdPath
                        {
                            Group = spawner.Group
                        });
                        
                        // Add spawn component
                        Ecb.AddComponent(entity, new SpawnComponent
                        {
                            SpawnTime = SystemAPI.Time.ElapsedTime
                        });
                    }
                    
                    // Update count
                    spawner.Count += spawnCount;
                }
            }
        }
    }
}
```

### SpawnTrackingSystem

The `SpawnTrackingSystem` adds `SpawnComponent` to newly spawned entities to track when they were created.

```csharp
[UpdateInGroup(typeof(InitializationSystemGroup))]
public partial class SpawnTrackingSystem : SystemBase
{
    private EntityQuery _newEntitiesQuery;
    private HashSet<Entity> _processedEntities = new HashSet<Entity>();

    protected override void OnCreate()
    {
        // Query for entities that have essential components but no SpawnComponent yet
        _newEntitiesQuery = GetEntityQuery(
            ComponentType.ReadOnly<LocalToWorld>(),
            ComponentType.ReadOnly<HealthComponent>(),
            ComponentType.Exclude<SpawnComponent>(),
            ComponentType.Exclude<DeadTag>()
        );
    }

    protected override void OnUpdate()
    {
        var ecb = new EntityCommandBuffer(Allocator.TempJob);
        float currentTime = (float)Time.ElapsedTime;
        
        // Process all entities that match our query
        using (var entities = _newEntitiesQuery.ToEntityArray(Unity.Collections.Allocator.Temp))
        {
            for (int i = 0; i < entities.Length; i++)
            {
                var entity = entities[i];
                
                // Skip if we've already processed this entity
                if (_processedEntities.Contains(entity))
                    continue;
                
                // Add to processed set
                _processedEntities.Add(entity);
                
                // Add SpawnComponent with current time
                ecb.AddComponent(entity, new SpawnComponent { SpawnTime = currentTime });
                
                // Log for debugging
                Debug.Log($"<color=cyan>Added SpawnComponent to entity {entity.Index} at time {currentTime}</color>");
            }
        }
        
        // Play the command buffer
        ecb.Playback(EntityManager);
        ecb.Dispose();
        
        // Clean up processed entities that no longer exist
        _processedEntities.RemoveWhere(e => !EntityManager.Exists(e));
    }
}
```

## Navigation and Movement

### Agent Navigation Components

The project uses the Agents Navigation package for enemy movement:

```csharp
// Agent body component
public struct AgentBody : IComponentData
{
    public float3 Destination;
    public float3 Velocity;
    public float Speed;
    public float Acceleration;
    public float StoppingDistance;
    public bool IsStopped;
}

// Agent avoidance component
public struct AgentAvoidance : IComponentData
{
    public float Radius;
    public float Strength;
}

// Agent crowd path component
public struct AgentCrowdPath : ISharedComponentData
{
    public Entity Group;
}
```

### Movement Systems

The movement systems handle enemy navigation and movement:

```csharp
[UpdateInGroup(typeof(AgentSystemGroup))]
public partial struct AgentMovementSystem : ISystem
{
    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        float deltaTime = SystemAPI.Time.DeltaTime;
        
        // Move all agents
        foreach (var (body, transform) in
                 SystemAPI.Query<RefRW<AgentBody>, RefRW<LocalTransform>>()
                 .WithNone<DeadTag>())
        {
            // Skip if stopped
            if (body.ValueRO.IsStopped)
                continue;
                
            // Calculate direction to destination
            float3 direction = body.ValueRO.Destination - transform.ValueRO.Position;
            float distance = math.length(direction);
            
            // Check if we've reached the destination
            if (distance <= body.ValueRO.StoppingDistance)
            {
                body.ValueRW.IsStopped = true;
                body.ValueRW.Velocity = float3.zero;
                continue;
            }
            
            // Normalize direction
            direction = math.normalize(direction);
            
            // Calculate target velocity
            float3 targetVelocity = direction * body.ValueRO.Speed;
            
            // Apply acceleration
            body.ValueRW.Velocity = math.lerp(
                body.ValueRO.Velocity,
                targetVelocity,
                body.ValueRO.Acceleration * deltaTime
            );
            
            // Update position
            transform.ValueRW.Position += body.ValueRO.Velocity * deltaTime;
            
            // Update rotation to face movement direction
            if (math.lengthsq(body.ValueRO.Velocity) > 0.01f)
            {
                transform.ValueRW.Rotation = quaternion.LookRotation(
                    math.normalize(body.ValueRO.Velocity),
                    math.up()
                );
            }
        }
    }
}
```

## AI Behavior

### EnemyAttackTriggerSystem

The `EnemyAttackTriggerSystem` randomly triggers attack animations for enemies.

```csharp
[UpdateBefore(typeof(EnemyAnimationSystem))]
public partial struct EnemyAttackTriggerSystem : ISystem
{
    private Unity.Mathematics.Random m_Random;
    private float m_AttackCheckTimer;
    private const float ATTACK_CHECK_INTERVAL = 1.0f;
    private const float ATTACK_CHANCE = 0.1f;
    
    public void OnUpdate(ref SystemState state)
    {
        // Update timer
        m_AttackCheckTimer += state.WorldUnmanaged.Time.DeltaTime;
        
        // Check if it's time to potentially trigger attacks
        if (m_AttackCheckTimer >= ATTACK_CHECK_INTERVAL)
        {
            m_AttackCheckTimer = 0;
            
            // Get current time
            float currentTime = (float)state.WorldUnmanaged.Time.ElapsedTime;
            
            // Process each enemy
            foreach (var (movementState, entity) in
                     SystemAPI.Query<RefRW<CharacterMovementState>>()
                     .WithAll<EnemyTag>()
                     .WithNone<DeadTag, HitTag>()
                     .WithEntityAccess())
            {
                // Skip if already attacking
                if (movementState.ValueRO.IsAttacking)
                    continue;
                    
                // Random chance to start attack
                if (m_Random.NextFloat() < ATTACK_CHANCE)
                {
                    movementState.ValueRW.IsAttacking = true;
                    movementState.ValueRW.AttackStartTime = currentTime;
                    movementState.ValueRW.AttackDuration = 1.5f;
                }
            }
            
            // Update random seed
            m_Random = Unity.Mathematics.Random.CreateFromIndex((uint)currentTime);
        }
    }
}
```

### Enemy AI State Machine

For more complex AI behavior, the project uses a state machine approach:

```csharp
// Enemy AI state enum
public enum EnemyAIState
{
    Idle,
    Patrol,
    Chase,
    Attack,
    Flee,
    Dead
}

// Enemy AI component
public struct EnemyAIComponent : IComponentData
{
    public EnemyAIState CurrentState;
    public EnemyAIState PreviousState;
    public float StateEnterTime;
    public float StateTimer;
    public Entity TargetEntity;
    public float3 HomePosition;
    public float3 PatrolDestination;
    public float DetectionRadius;
    public float AttackRadius;
    public float FleeHealthThreshold;
}

// Enemy AI system
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateBefore(typeof(AgentMovementSystem))]
public partial class EnemyAISystem : SystemBase
{
    protected override void OnUpdate()
    {
        float currentTime = (float)Time.ElapsedTime;
        float deltaTime = Time.DeltaTime;
        
        // Process each enemy AI
        Entities
            .WithNone<DeadTag>()
            .ForEach((Entity entity, ref EnemyAIComponent ai, ref AgentBody body, in LocalToWorld transform) =>
            {
                // Update state timer
                ai.StateTimer += deltaTime;
                
                // Handle state transitions
                switch (ai.CurrentState)
                {
                    case EnemyAIState.Idle:
                        // Transition to patrol after idle time
                        if (ai.StateTimer >= 3.0f)
                        {
                            TransitionToState(ref ai, EnemyAIState.Patrol, currentTime);
                            
                            // Set random patrol destination
                            float angle = UnityEngine.Random.Range(0f, 2f * math.PI);
                            float distance = UnityEngine.Random.Range(5f, 15f);
                            ai.PatrolDestination = ai.HomePosition + new float3(
                                math.cos(angle) * distance,
                                0f,
                                math.sin(angle) * distance
                            );
                            
                            // Set agent destination
                            body.Destination = ai.PatrolDestination;
                            body.IsStopped = false;
                        }
                        break;
                        
                    case EnemyAIState.Patrol:
                        // Check if reached patrol destination
                        float distToPatrol = math.length(transform.Position - ai.PatrolDestination);
                        if (distToPatrol <= body.StoppingDistance)
                        {
                            TransitionToState(ref ai, EnemyAIState.Idle, currentTime);
                            body.IsStopped = true;
                        }
                        break;
                        
                    // Additional states and transitions...
                }
            })
            .WithoutBurst() // Can't use Burst with UnityEngine.Random
            .Run();
    }
    
    private void TransitionToState(ref EnemyAIComponent ai, EnemyAIState newState, float currentTime)
    {
        ai.PreviousState = ai.CurrentState;
        ai.CurrentState = newState;
        ai.StateEnterTime = currentTime;
        ai.StateTimer = 0f;
    }
}
```

## Performance Optimization

### Entity Pooling

For better performance, the project implements entity pooling instead of constantly creating and destroying entities:

```csharp
public class EntityPoolSystem : SystemBase
{
    private NativeList<Entity> _inactivePool;
    private int _poolSize = 150;
    private Entity _prefabEntity;
    
    protected override void OnCreate()
    {
        _inactivePool = new NativeList<Entity>(_poolSize, Allocator.Persistent);
        
        // Get prefab entity
        var spawnerQuery = GetEntityQuery(ComponentType.ReadOnly<EnemySpawnComponent>());
        if (!spawnerQuery.IsEmpty)
        {
            var spawner = spawnerQuery.GetSingleton<EnemySpawnComponent>();
            _prefabEntity = spawner.enemyPrefab;
            
            // Pre-populate pool
            PrePopulatePool();
        }
    }
    
    private void PrePopulatePool()
    {
        // Create entities and add to inactive pool
        for (int i = 0; i < _poolSize; i++)
        {
            Entity entity = EntityManager.Instantiate(_prefabEntity);
            
            // Disable entity
            EntityManager.AddComponent<DisabledTag>(entity);
            
            // Add to inactive pool
            _inactivePool.Add(entity);
        }
    }
    
    public Entity GetEntityFromPool()
    {
        if (_inactivePool.Length == 0)
        {
            // Pool is empty, create a new entity
            Entity entity = EntityManager.Instantiate(_prefabEntity);
            return entity;
        }
        
        // Get entity from pool
        Entity pooledEntity = _inactivePool[_inactivePool.Length - 1];
        _inactivePool.RemoveAt(_inactivePool.Length - 1);
        
        // Enable entity
        EntityManager.RemoveComponent<DisabledTag>(pooledEntity);
        
        return pooledEntity;
    }
    
    public void ReturnEntityToPool(Entity entity)
    {
        // Reset entity state
        if (EntityManager.HasComponent<HealthComponent>(entity))
        {
            var health = EntityManager.GetComponentData<HealthComponent>(entity);
            health.CurrentHealth = health.MaxHealth;
            EntityManager.SetComponentData(entity, health);
        }
        
        // Remove tags
        EntityManager.RemoveComponent<DetectedTag>(entity);
        EntityManager.RemoveComponent<InFOVTag>(entity);
        EntityManager.RemoveComponent<CurrentTargetTag>(entity);
        EntityManager.RemoveComponent<DeadTag>(entity);
        EntityManager.RemoveComponent<HitTag>(entity);
        
        // Disable entity
        EntityManager.AddComponent<DisabledTag>(entity);
        
        // Add to inactive pool
        _inactivePool.Add(entity);
    }
    
    protected override void OnDestroy()
    {
        if (_inactivePool.IsCreated)
        {
            _inactivePool.Dispose();
        }
    }
    
    protected override void OnUpdate()
    {
        // This system doesn't need an update
    }
}
```

### Dynamic Entity Count

The project adjusts the maximum entity count based on device performance:

```csharp
public class DynamicEntityCountSystem : SystemBase
{
    private float _performanceCheckTimer;
    private const float PERFORMANCE_CHECK_INTERVAL = 5.0f;
    private const float TARGET_FRAMERATE = 30.0f;
    private const int MIN_ENTITY_COUNT = 20;
    private const int MAX_ENTITY_COUNT = 200;
    
    protected override void OnUpdate()
    {
        _performanceCheckTimer += Time.DeltaTime;
        
        if (_performanceCheckTimer >= PERFORMANCE_CHECK_INTERVAL)
        {
            _performanceCheckTimer = 0f;
            
            // Get current framerate
            float currentFramerate = 1.0f / Time.DeltaTime;
            
            // Get all spawners
            Entities
                .WithAll<EnemySpawnComponent>()
                .ForEach((ref EnemySpawnComponent spawner) =>
                {
                    // Adjust max entity count based on performance
                    if (currentFramerate < TARGET_FRAMERATE * 0.8f)
                    {
                        // Reduce entity count
                        spawner.maxEnemyCount = math.max(MIN_ENTITY_COUNT, spawner.maxEnemyCount - 10);
                    }
                    else if (currentFramerate > TARGET_FRAMERATE * 1.2f)
                    {
                        // Increase entity count
                        spawner.maxEnemyCount = math.min(MAX_ENTITY_COUNT, spawner.maxEnemyCount + 5);
                    }
                })
                .WithoutBurst() // Can't use Burst with Time.DeltaTime
                .Run();
        }
    }
}
```

## Implementation Guidelines

When implementing or modifying enemy spawning and AI systems, follow these guidelines:

1. **Spawning Strategy**:
   - Use spatial distribution to avoid clumping
   - Implement spawn waves with increasing difficulty
   - Consider using entity pooling for better performance
   - Add spawn effects (particles, sounds) for visual feedback

2. **Navigation and Movement**:
   - Use the Agents Navigation package for pathfinding
   - Implement avoidance to prevent collisions between entities
   - Add variation to movement speed and patterns
   - Consider using navmesh for complex environments

3. **AI Behavior**:
   - Implement a state machine for complex behaviors
   - Add perception systems for enemy awareness
   - Create different enemy types with unique behaviors
   - Balance difficulty through AI parameters

4. **Performance Optimization**:
   - Limit the maximum number of active entities
   - Implement entity pooling to reduce allocation overhead
   - Use dynamic entity count based on device performance
   - Apply LOD (Level of Detail) for distant entities

5. **Spawn Configuration**:
   - Use ScriptableObjects for spawn configuration
   - Implement wave-based spawning for progression
   - Add spawn point management for strategic placement
   - Create spawn director for coordinated enemy attacks

6. **Integration with Other Systems**:
   - Coordinate with the detection system for player awareness
   - Integrate with the animation system for movement and attacks
   - Connect with the health system for damage and death
   - Link with the weapon system for enemy attacks
