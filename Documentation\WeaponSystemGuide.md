# Weapon System Guide: Creating New Bullet Types

This guide explains how to create new bullet types with different speeds and fire rates in the game using the new configuration-based weapon system.

## Table of Contents

1. [Overview](#overview)
2. [Weapon Configuration](#weapon-configuration)
3. [Bullet Configuration](#bullet-configuration)
4. [Creating New Bullet Types](#creating-new-bullet-types)
5. [Modifying Fire Rate](#modifying-fire-rate)
6. [Upgrading Weapons and Bullets](#upgrading-weapons-and-bullets)
7. [Advanced Bullet Features](#advanced-bullet-features)
8. [Example Configurations](#example-configurations)

## Overview

The weapon system uses ScriptableObjects for configuration, making it easy to create and modify weapons and bullets without changing code. The key components are:

- **WeaponConfig**: Defines weapon properties and upgrade levels
- **BulletConfig**: Defines bullet properties and upgrade levels
- **WeaponUpgradeManager**: Handles weapon and bullet upgrades at runtime

## Weapon Configuration

Weapons are configured using the `WeaponConfig` ScriptableObject:

1. In the Project window, right-click and select **Create > Weapon > Weapon Configuration**
2. Set the basic properties:
   - **Weapon Name**: Display name for the weapon
   - **Weapon Type**: Type of weapon (Pistol, Mp4, etc.)
   - **Default Bullet Type**: The default bullet type for this weapon
   - **Base Max Ammo**: Starting ammo capacity
   - **Base Reload Time**: Base reload time in seconds

3. Add upgrade levels by clicking the "+" button in the Upgrade Levels list:
   - **Level Name**: Name for this upgrade level
   - **Upgrade Cost**: Cost to upgrade to this level
   - **Stats**: Weapon stats for this level (fire rate, accuracy, etc.)
   - **Additional Ammo**: Extra ammo capacity at this level
   - **Reload Time Reduction**: Percentage reduction in reload time

## Bullet Configuration

Bullets are configured using the `BulletConfig` ScriptableObject:

1. In the Project window, right-click and select **Create > Weapon > Bullet Configuration**
2. Set the basic properties:
   - **Bullet Name**: Display name for the bullet
   - **Bullet Type**: Type of bullet (Normal, Fire, etc.)
   - **Bullet Prefab**: Reference to the bullet prefab

3. Add upgrade levels by clicking the "+" button in the Upgrade Levels list:
   - **Level Name**: Name for this upgrade level
   - **Upgrade Cost**: Cost to upgrade to this level
   - **Stats**: Bullet stats for this level (speed, damage multiplier, etc.)
   - **Special Effects**: Settings for penetration, explosion, etc.

## Creating New Bullet Types

To create a new bullet type:

1. **Add a new bullet type to the enum**:
   - If needed, add a new value to the `BulletType` enum in `BulletTypeData.cs`
   - Example: `Explosive`, `Piercing`, etc.

2. **Create a bullet configuration**:
   - Create a new `BulletConfig` ScriptableObject
   - Set the bullet type to your new type
   - Configure the stats for each upgrade level

3. **Create a bullet prefab**:
   - Duplicate an existing bullet prefab
   - Add the `BulletAuthoring` component
   - Assign your new `BulletConfig` to the component
   - Customize the visual appearance (mesh, materials, effects)

4. **Register with the WeaponUpgradeManager**:
   - Add your new `BulletConfig` to the `availableBullets` list in the WeaponUpgradeManager

## Modifying Fire Rate

The fire rate is defined in the `WeaponConfig` ScriptableObject:

1. Open your weapon configuration asset
2. For each upgrade level, set the `fireRate` value in the stats:
   - Higher values = more shots per second (faster firing)
   - Lower values = fewer shots per second (slower firing)

Example values:
- Slow pistol: 2-5 shots per second
- Medium SMG: 10-15 shots per second
- Fast machine gun: 20-30 shots per second

## Upgrading Weapons and Bullets

Weapons and bullets can be upgraded at runtime using the `WeaponUpgradeManager`:

```csharp
// Get the WeaponUpgradeManager instance
var upgradeManager = WeaponUpgradeManager.Instance;

// Upgrade a weapon
upgradeManager.UpgradeWeapon(WeaponType.Pistol);

// Upgrade a bullet
upgradeManager.UpgradeBullet(BulletType.Normal);

// Get current weapon level
int pistolLevel = upgradeManager.GetWeaponLevel(WeaponType.Pistol);

// Get current bullet level
int normalBulletLevel = upgradeManager.GetBulletLevel(BulletType.Normal);
```

## Advanced Bullet Features

The new bullet system supports several advanced features:

### Penetration

Bullets can penetrate through enemies and hit multiple targets:

```csharp
bulletStats.canPenetrate = true;
bulletStats.maxPenetrations = 2; // Number of enemies it can pass through
bulletStats.penetrationDamageReduction = 0.3f; // Damage reduction per penetration (0-1)
```

### Explosion

Bullets can explode on impact, dealing area damage:

```csharp
bulletStats.explodeOnImpact = true;
bulletStats.explosionRadius = 5f; // Radius in units
bulletStats.explosionDamageMultiplier = 0.7f; // Damage multiplier for explosion
```

### Custom Visual Effects

You can assign custom visual effects to bullets:

```csharp
bulletStats.impactEffectPrefab = myImpactEffect;
bulletStats.trailEffectPrefab = myTrailEffect;
```

## Example Configurations

The system includes several example configurations:

### Weapons
- **Pistol**: Balanced weapon with moderate fire rate and damage
- **MP4**: Fast-firing weapon with lower damage per shot

### Bullets
- **Normal**: Standard bullet with balanced stats
- **Fire**: Higher damage but slower speed
- **Explosive**: Explodes on impact, dealing area damage
- **Piercing**: Can penetrate through multiple enemies

To create these example configurations, use the menu option:
**Tools > Weapon System > Create Example Configs**

## Conclusion

This configuration-based weapon system makes it easy to create and modify weapons and bullets without changing code. By using ScriptableObjects, you can quickly iterate on different weapon and bullet types, and the upgrade system allows for progression throughout the game.

For more advanced customization, you can extend the system by adding new properties to the configuration classes and implementing the corresponding behavior in the game systems.