# ECS Technical Documentation - Part 3: Weapons and Shooting

## Introduction

This document details the weapon and shooting systems used in the project. These systems are responsible for weapon management, aiming, firing, and bullet behavior.

## Table of Contents

1. [Weapon Components](#weapon-components)
2. [Weapon Configuration](#weapon-configuration)
3. [Weapon Systems](#weapon-systems)
4. [Bullet Systems](#bullet-systems)
5. [Implementation Guidelines](#implementation-guidelines)

## Weapon Components

### Core Weapon Components

```csharp
// Weapon state component
public struct WeaponStateComponent : IComponentData
{
    public Entity Owner;
    public float LastFireTime;
    public float CooldownTime;
    public bool CanFire;
    public int CurrentAmmo;
    public int MaxAmmo;
    public float ReloadTime;
    public bool IsReloading;
    public float ReloadStartTime;
}

// Weapon body part component
public struct WeaponBodyPartComponent : IComponentData
{
    public Entity TargetEntity;
    public float3 Position;
    public float3 Direction;
    public float3 UpDirection;
    public quaternion Rotation;
    public bool HasTarget;
}

// Player shooting input data
public struct PlayerShootingInputData : IComponentData
{
    public bool IsShooting;
    public float LastShootTime;
    public float CoolDownTime;
}

// Weapon detection settings
public struct WeaponDetectionSettings : IComponentData
{
    public float DetectionRadius;
    public float DetectionRange;
    public float DetectionAngle;
    public float ConeAngle;
    public float ConeRange;
}
```

### Bullet Components

```csharp
// Bullet component
public struct BulletComponent : IComponentData
{
    public Entity Owner;
    public float3 Direction;
    public float Speed;
    public float Damage;
    public float MaxDistance;
    public float DistanceTraveled;
    public float CreationTime;
    public float LifeTime;
    public BulletType Type;
}

// Bullet type enum
public enum BulletType
{
    Standard,
    Piercing,
    Explosive,
    Homing
}
```

## Weapon Configuration

Weapons are configured using ScriptableObjects to allow for easy modification and extension.

### WeaponConfig

The `WeaponConfig` ScriptableObject defines all properties of a weapon:

```csharp
[CreateAssetMenu(fileName = "WeaponConfig", menuName = "Weapon/Weapon Configuration")]
public class WeaponConfig : ScriptableObject
{
    [Header("Basic Properties")]
    public string weaponName;
    public WeaponSubModuleState weaponType;
    public BulletType defaultBulletType;
    public int baseMaxAmmo = 30;
    public float baseReloadTime = 2f;

    [Header("Base Stats")]
    public WeaponStats baseStats = new WeaponStats
    {
        fireRate = 10f,
        accuracy = 0.9f,
        maxRange = 100f,
        damageAmount = 10f,
        spreadAngle = 2f,
        recoilAmount = 1f,
        recoilRecovery = 2f
    };

    [Header("Detection Settings")]
    public float detectionRadius = 10f;
    public float detectionRange = 20f;
    public float detectionAngle = 45f;
    public float coneAngle = 45f;
    public float coneRange = 20f;

    [Header("Upgrade Levels")]
    public List<WeaponUpgradeLevel> upgradeLevels = new List<WeaponUpgradeLevel>();
}

// Weapon stats structure
[Serializable]
public struct WeaponStats
{
    public float fireRate;
    public float accuracy;
    public float maxRange;
    public float damageAmount;
    public float spreadAngle;
    public float recoilAmount;
    public float recoilRecovery;
}

// Weapon upgrade level
[Serializable]
public class WeaponUpgradeLevel
{
    public string levelName;
    public int upgradeCost;
    public WeaponStats stats;
    public float detectionRadiusBonus;
    public float detectionRangeBonus;
    public float detectionAngleBonus;
}
```

### FireBulletConfig

The `FireBulletConfig` ScriptableObject defines bullet properties:

```csharp
[CreateAssetMenu(fileName = "FireBulletConfig", menuName = "Weapon/Bullet Configuration")]
public class FireBulletConfig : ScriptableObject
{
    [Header("Basic Properties")]
    public BulletType bulletType;
    public float bulletSpeed = 50f;
    public float bulletDamage = 10f;
    public float bulletLifetime = 5f;
    public float bulletSize = 0.1f;

    [Header("Special Properties")]
    public float explosionRadius = 2f;
    public float piercingCount = 3;
    public float homingStrength = 0.5f;
}
```

## Weapon Systems

### WeaponDetectionSettingsSystem

The `WeaponDetectionSettingsSystem` updates weapon detection settings based on the current weapon configuration.

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class WeaponDetectionSettingsSystem : SystemBase
{
    protected override void OnUpdate()
    {
        // Get the current weapon configuration
        var weaponModule = WeaponModule.Instance;
        if (weaponModule == null || weaponModule.CurrentWeaponConfig == null)
            return;
            
        var config = weaponModule.CurrentWeaponConfig;
        var level = weaponModule.CurrentWeaponLevel;
        
        // Update detection settings for all player entities
        Entities
            .WithAll<PlayerTag>()
            .ForEach((ref WeaponDetectionSettings settings) =>
            {
                // Set base detection parameters
                settings.DetectionRadius = config.detectionRadius;
                settings.DetectionRange = config.detectionRange;
                settings.DetectionAngle = config.detectionAngle;
                settings.ConeAngle = config.coneAngle;
                settings.ConeRange = config.coneRange;
                
                // Apply upgrades if available
                if (level > 0 && level <= config.upgradeLevels.Count)
                {
                    var upgrade = config.upgradeLevels[level - 1];
                    settings.DetectionRadius += upgrade.detectionRadiusBonus;
                    settings.DetectionRange += upgrade.detectionRangeBonus;
                    settings.DetectionAngle += upgrade.detectionAngleBonus;
                }
            })
            .Schedule();
    }
}
```

### WeaponAimSystem

The `WeaponAimSystem` handles weapon aiming based on the current target.

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateBefore(typeof(OptimizedShootingSystem))]
public partial class WeaponAimSystem : SystemBase
{
    protected override void OnUpdate()
    {
        // Get the current target
        if (detectionTargetQuery.IsEmpty)
            return;
            
        var targetData = detectionTargetQuery.GetSingleton<DetectionTargetComponent>();
        
        // Update weapon aim for all weapon entities
        Entities
            .WithAll<WeaponStateComponent>()
            .ForEach((ref WeaponBodyPartComponent weaponBody) =>
            {
                if (targetData.HasTarget)
                {
                    // Set target information
                    weaponBody.TargetEntity = targetData.CurrentTarget;
                    weaponBody.Position = targetData.CurrentPosition;
                    weaponBody.HasTarget = true;
                    
                    // Calculate aim direction
                    var direction = math.normalize(targetData.CurrentPosition - weaponBody.Position);
                    weaponBody.Direction = direction;
                    
                    // Calculate rotation to face target
                    weaponBody.Rotation = quaternion.LookRotation(direction, math.up());
                }
                else
                {
                    // No target, reset values
                    weaponBody.HasTarget = false;
                    weaponBody.TargetEntity = Entity.Null;
                }
            })
            .Schedule();
    }
}
```

### OptimizedShootingSystem

The `OptimizedShootingSystem` handles weapon firing and bullet creation.

```csharp
[UpdateInGroup(typeof(FixedStepSimulationSystemGroup))]
[UpdateAfter(typeof(PhysicsSystemGroup))]
public partial class OptimizedShootingSystem : SystemBase
{
    protected override void OnUpdate()
    {
        // Get command buffer for entity creation
        var ecb = commandBufferSystem.CreateCommandBuffer().AsParallelWriter();
        var deltaTime = Time.DeltaTime;
        var currentTime = Time.ElapsedTime;
        
        // Get bullet prefabs and configuration
        var bulletPrefabs = new NativeArray<Entity>(bulletPrefabEntities, Allocator.TempJob);
        var bulletTypeDataArray = new NativeArray<BulletTypeData>(bulletTypeData, Allocator.TempJob);
        
        // Get detection target
        var detectionTarget = detectionTargetQuery.GetSingleton<DetectionTargetComponent>();
        
        // Schedule weapon update job
        var weaponJob = new WeaponUpdateJob
        {
            DeltaTime = deltaTime,
            CurrentTime = currentTime,
            ECB = ecb,
            DetectionTarget = detectionTarget,
            BulletPrefabs = bulletPrefabs,
            BulletTypeDataArray = bulletTypeDataArray
        };
        
        var weaponHandle = weaponJob.Schedule(Dependency);
        
        // Schedule bullet update job
        var bulletJob = new BulletUpdateJob
        {
            DeltaTime = deltaTime,
            PhysicsWorld = physicsWorld,
            ECB = ecb
        };
        
        Dependency = bulletJob.Schedule(weaponHandle);
        commandBufferSystem.AddJobHandleForProducer(Dependency);
    }
}
```

## Bullet Systems

### MoveForwardSystem

The `MoveForwardSystem` moves bullets forward based on their direction and speed.

```csharp
[BurstCompile]
partial struct MoveForwardSystem : ISystem
{
    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
            .CreateCommandBuffer(state.WorldUnmanaged);
            
        float deltaTime = SystemAPI.Time.DeltaTime;
        float currentTime = (float)SystemAPI.Time.ElapsedTime;
        
        // Move all bullets forward
        foreach (var (transform, bullet, entity) in
                 SystemAPI.Query<RefRW<LocalTransform>, RefRW<BulletComponent>>().WithEntityAccess())
        {
            // Update position based on direction and speed
            transform.ValueRW.Position += bullet.ValueRO.Direction * bullet.ValueRO.Speed * deltaTime;
            
            // Update distance traveled
            bullet.ValueRW.DistanceTraveled += bullet.ValueRO.Speed * deltaTime;
            
            // Check if bullet has exceeded max distance or lifetime
            if (bullet.ValueRO.DistanceTraveled >= bullet.ValueRO.MaxDistance ||
                currentTime - bullet.ValueRO.CreationTime >= bullet.ValueRO.LifeTime)
            {
                ecb.DestroyEntity(entity);
            }
        }
    }
}
```

### BulletCollisionSystem

The `BulletCollisionSystem` handles bullet collisions with entities.

```csharp
[BurstCompile]
partial struct BulletCollisionSystem : ISystem
{
    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
            .CreateCommandBuffer(state.WorldUnmanaged);
            
        var physicsWorld = SystemAPI.GetSingleton<PhysicsWorldSingleton>().PhysicsWorld;
        
        // Check collisions for all bullets
        foreach (var (transform, bullet, entity) in
                 SystemAPI.Query<RefRO<LocalTransform>, RefRO<BulletComponent>>().WithEntityAccess())
        {
            // Create a ray from the bullet's position in its direction
            var rayStart = transform.ValueRO.Position;
            var rayEnd = rayStart + bullet.ValueRO.Direction * bullet.ValueRO.Speed * SystemAPI.Time.DeltaTime;
            
            // Cast the ray to check for collisions
            var raycastInput = new RaycastInput
            {
                Start = rayStart,
                End = rayEnd,
                Filter = new CollisionFilter
                {
                    BelongsTo = 1u << 0, // Bullet layer
                    CollidesWith = 1u << 1 // Enemy layer
                }
            };
            
            if (physicsWorld.CastRay(raycastInput, out var hit))
            {
                // Get the hit entity
                var hitEntity = physicsWorld.Bodies[hit.RigidBodyIndex].Entity;
                
                // Apply damage if the entity has a health component
                if (SystemAPI.HasComponent<HealthComponent>(hitEntity))
                {
                    var damageEvent = new DamageEvent
                    {
                        Target = hitEntity,
                        Amount = bullet.ValueRO.Damage,
                        Source = bullet.ValueRO.Owner,
                        HitPoint = hit.Position
                    };
                    
                    ecb.AddComponent(hitEntity, damageEvent);
                }
                
                // Handle special bullet types
                switch (bullet.ValueRO.Type)
                {
                    case BulletType.Standard:
                        // Destroy the bullet on impact
                        ecb.DestroyEntity(entity);
                        break;
                        
                    case BulletType.Piercing:
                        // Piercing bullets continue through targets
                        // Implement piercing logic
                        break;
                        
                    case BulletType.Explosive:
                        // Explosive bullets create an explosion
                        // Implement explosion logic
                        break;
                }
            }
        }
    }
}
```

## Implementation Guidelines

When implementing or modifying weapon and shooting systems, follow these guidelines:

1. **Weapon Configuration**:
   - Use ScriptableObjects for weapon configuration
   - Implement a clear upgrade path for weapons
   - Centralize detection parameters in the weapon config
   - Use separate configs for different bullet types

2. **Performance Considerations**:
   - Use Burst compilation for bullet systems
   - Implement bullet pooling for better performance
   - Limit the maximum number of active bullets
   - Use efficient collision detection methods

3. **Weapon Behavior**:
   - Implement different firing patterns (single, burst, automatic)
   - Add weapon recoil and spread for realistic behavior
   - Implement reload mechanics with appropriate animations
   - Add visual and audio feedback for weapon firing

4. **Bullet Behavior**:
   - Implement different bullet types with unique behaviors
   - Add visual effects for bullet trails and impacts
   - Implement bullet penetration and ricochet mechanics
   - Add area-of-effect damage for explosive bullets

5. **Integration with Other Systems**:
   - Coordinate with animation systems for proper firing animations
   - Integrate with the targeting system for accurate aiming
   - Implement weapon switching with appropriate transitions
   - Add weapon pickup and inventory management
