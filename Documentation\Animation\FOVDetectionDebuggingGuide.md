# FOV Detection Debugging Guide

## 🎯 Problem Description

The issue is that enemies are not being correctly detected as "in FOV" (Field of View), causing targeting problems when enemies surround the player. This leads to:
- Difficulty selecting new targets after killing current target
- Inconsistent targeting behavior
- Rapid state changes in animation system

## 🔍 Detailed Logging Added

I've added comprehensive logging to the `AimingModule.cs` to help identify exactly why FOV detection is failing:

### 1. **ECS Target Data Logging**
```csharp
[AimingModule] ECS Target Data: HasTarget=true, IsInFOV=false, Position=(x,y,z), Score=1.23
```

### 2. **Enemy Detection Logging**
```csharp
[AimingModule] Detected 3 enemies from ECS
[AimingModule] Enemy 0: Pos=(1,0,2), ECS_IsInFOV=false, Manual_IsInFOV=true, Angle=25.0°, Distance=3.2m, ConeAngle=60.0°, ConeRange=10.0m, Score=1.50
[AimingModule] Enemy 1: Pos=(-1,0,3), ECS_IsInFOV=true, Manual_IsInFOV=true, Angle=35.0°, Distance=4.1m, ConeAngle=60.0°, ConeRange=10.0m, Score=2.10
```

### 3. **FOV Mismatch Detection**
```csharp
[AimingModule] ⚠️ FOV MISMATCH for Enemy 0: ECS=false, Manual=true
```

### 4. **Target Selection Logging**
```csharp
[AimingModule] FindBestTarget: Evaluating 3 enemies
[AimingModule] Enemy 0: IsInFOV=false, Distance=3.2m, Score=1.50
[AimingModule] Enemy 1: IsInFOV=true, Distance=4.1m, Score=2.10
[AimingModule] Selected best target: IsInFOV=true, Distance=4.1m, Score=2.10
```

### 5. **Target Validation Logging**
```csharp
[AimingModule] Cached target validation: IsInFOV=true, AllowNonFOV=true, IsValid=true
[AimingModule] Non-cached target validation: IsInCone=true, IsInRange=true, IsTargetBehind=false, AllowNonFOV=true, IsValid=true
```

## 🧪 How to Use the Debug Logging

### Step 1: Enable Debug Logging
1. **In Unity Inspector:**
   - Select your player character
   - Find the `AimingModule` component
   - In the `AimingConfiguration` asset:
     - Set `Show Target Debug` = ✅ `true`

2. **In DebugLogManager:**
   - Enable `PlayerAiming` log type
   - Set appropriate log level

### Step 2: Test Scenario
1. **Setup Test Environment:**
   - Place player in center
   - Surround with 3-4 enemies at different angles
   - Some enemies in FOV, some behind player

2. **Kill Current Target:**
   - Target and kill one enemy
   - Observe console logs during target switching

### Step 3: Analyze Log Output

Look for these key indicators:

#### **✅ Normal Operation:**
```
[AimingModule] Detected 3 enemies from ECS
[AimingModule] Enemy 0: ECS_IsInFOV=true, Manual_IsInFOV=true, Angle=25.0°
[AimingModule] Selected best target: IsInFOV=true, Distance=3.2m
```

#### **❌ FOV Detection Issues:**
```
[AimingModule] ⚠️ FOV MISMATCH for Enemy 0: ECS=false, Manual=true
[AimingModule] Enemy 0: ECS_IsInFOV=false, Manual_IsInFOV=true, Angle=25.0°
```

#### **❌ No Enemies Detected:**
```
[AimingModule] Detected 0 enemies from ECS
[AimingModule] FindBestTarget: No enemies provided
```

#### **❌ All Enemies Outside FOV:**
```
[AimingModule] Enemy 0: ECS_IsInFOV=false, Manual_IsInFOV=false, Angle=95.0°
[AimingModule] Enemy 1: ECS_IsInFOV=false, Manual_IsInFOV=false, Angle=120.0°
```

## 🔧 Common Issues and Solutions

### Issue 1: ECS vs Manual FOV Mismatch
**Symptoms:** `⚠️ FOV MISMATCH` messages in console
**Cause:** ECS detection system and AimingModule using different FOV calculations
**Solution:** 
- Check `currentConeAngle` vs ECS `DetectionAngle`
- Verify `coneRange` vs ECS `DetectionRange`
- Ensure ECS parameters are updated correctly

### Issue 2: No Enemies Detected
**Symptoms:** `Detected 0 enemies from ECS`
**Cause:** ECS detection system not finding enemies
**Solution:**
- Check if `OptimizedDetectionSystem` is running
- Verify enemy entities have required components
- Check collision layers and filters

### Issue 3: All Enemies Outside FOV
**Symptoms:** All enemies show `ECS_IsInFOV=false`
**Cause:** FOV angle too narrow or enemies positioned outside cone
**Solution:**
- Increase `coneAngle` in AimingConfiguration
- Check if `allowNonFOVTargets` should be enabled
- Verify player forward direction

### Issue 4: Angle Calculation Issues
**Symptoms:** Angle values don't match expected positions
**Cause:** Different coordinate systems or forward directions
**Solution:**
- Verify `playerTransform.forward` direction
- Check if ECS uses different coordinate system
- Ensure consistent angle calculation methods

## 🎮 Debug Commands

### Manual Testing Commands
```csharp
// In AimingModule, add these debug methods:

[ContextMenu("Debug Current FOV State")]
public void DebugCurrentFOVState()
{
    Debug.Log($"Current Cone Angle: {currentConeAngle}");
    Debug.Log($"Cone Range: {coneRange}");
    Debug.Log($"Allow Non-FOV Targets: {aimingConfig.allowNonFOVTargets}");
    Debug.Log($"Cached Enemies: {m_cachedDetectedEnemies.Count}");
}

[ContextMenu("Force Target Search")]
public void ForceTargetSearch()
{
    UpdateDetectionCache();
}
```

### Runtime Debugging
```csharp
// Check ECS system status
var world = World.DefaultGameObjectInjectionWorld;
var detectionSystem = world.GetExistingSystemManaged<OptimizedDetectionSystem>();
Debug.Log($"Detection System Active: {detectionSystem != null}");

// Check detection parameters
Debug.Log($"ECS Detection Angle: {detectionSystem.DetectionAngle}");
Debug.Log($"ECS Detection Range: {detectionSystem.DetectionRange}");
```

## 📊 Expected Log Flow

### Normal Target Switching:
1. `ECS Target Data: HasTarget=true, IsInFOV=true`
2. `Detected 3 enemies from ECS`
3. `Enemy 0: ECS_IsInFOV=true, Manual_IsInFOV=true`
4. `FindBestTarget: Evaluating 3 enemies`
5. `Selected best target: IsInFOV=true`
6. `Cached target validation: IsValid=true`

### Problem Scenarios:
1. **FOV Mismatch:** ECS and Manual calculations differ
2. **No Detection:** ECS system not detecting enemies
3. **Wrong Angles:** Angle calculations incorrect
4. **Parameter Sync:** ECS parameters not updated from AimingModule

## 🚀 Next Steps

1. **Run the test scenario** with debug logging enabled
2. **Analyze the console output** using this guide
3. **Identify the specific issue** from the log patterns
4. **Apply the appropriate solution** based on the issue type
5. **Verify the fix** by checking for normal log flow

The detailed logging will help pinpoint exactly where the FOV detection is failing and why enemies aren't being correctly identified as "in FOV". 🎯
