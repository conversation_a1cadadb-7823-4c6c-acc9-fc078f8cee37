# ECS Technical Documentation - Part 5: Animation

## Introduction

This document details the animation systems used in the project. These systems are responsible for managing character animations, transitions between animation states, and special animation effects like hit reactions and death animations.

## Table of Contents

1. [Animation Components](#animation-components)
2. [Animation Systems](#animation-systems)
3. [Animation Integration](#animation-integration)
4. [Special Animation Effects](#special-animation-effects)
5. [Implementation Guidelines](#implementation-guidelines)

## Animation Components

### Core Animation Components

```csharp
// Character animator reference
public struct CharacterAnimatorReference : IComponentData
{
    public Entity AnimatorEntity;
}

// Character movement state
public struct CharacterMovementState : IComponentData
{
    public int CurrentAnimationID;
    public bool IsMoving;
    public bool IsAttacking;
    public float MoveSpeed;
    public float AttackStartTime;
    public float AttackDuration;
}

// Character settings
public struct CharacterSettings : IComponentData
{
    public float MoveThreshold;
    public float AttackDuration;
    public float HitReactionDuration;
}

// Animation tags
public struct HitAnimationStartedTag : IComponentData { }
public struct DeathAnimationStartedTag : IComponentData { }
```

### Animation Enums

```csharp
// Enemy animation IDs
public enum EnemyAnimationIDs
{
    Idle = 0,
    Walk = 1,
    Run = 2,
    Attack = 3,
    Dead = 4,
    Hit = 5
}

// Player animation IDs
public enum PlayerAnimationIDs
{
    Idle = 0,
    Walk = 1,
    Run = 2,
    Shoot = 3,
    Reload = 4,
    Hit = 5
}
```

## Animation Systems

### EnemyAnimationSystem

The `EnemyAnimationSystem` manages animations for enemy entities based on their movement state.

```csharp
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class EnemyAnimationSystem : SystemBase
{
    private EntityQuery m_AnimatorQuery;
    private ComponentLookup<AnimatorControlComponent> AnimatorControlLookup;
    private float m_LogTimer;
    private const float LOG_INTERVAL = 5.0f;

    protected override void OnCreate()
    {
        m_AnimatorQuery = GetEntityQuery(ComponentType.ReadOnly<AnimatorControlComponent>());
        AnimatorControlLookup = GetComponentLookup<AnimatorControlComponent>(false);
    }

    protected override void OnUpdate()
    {
        AnimatorControlLookup.Update(this);
        
        // Update log timer
        m_LogTimer += Time.DeltaTime;
        bool shouldLog = m_LogTimer >= LOG_INTERVAL;
        if (shouldLog)
        {
            m_LogTimer = 0;
            DebugLogManager.Instance.Log("[EnemyAnimationSystem] Updating animations", DebugLogManager.LogType.EnemyAnimation);
        }
        
        // First, handle dead entities separately
        foreach (var (movementState, animatorRef, deadTag, entity) in
                 SystemAPI.Query<RefRW<CharacterMovementState>, RefRO<CharacterAnimatorReference>, RefRO<DeadTag>>().WithEntityAccess())
        {
            if (animatorRef.ValueRO.AnimatorEntity == Entity.Null)
                continue;

            // Always force the dead animation for dead entities
            movementState.ValueRW.CurrentAnimationID = (int)EnemyAnimationIDs.Dead;
            movementState.ValueRW.IsMoving = false;
            movementState.ValueRW.IsAttacking = false;

            if (shouldLog)
            {
                DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Entity {entity.Index} is dead, ensuring Dead animation is set", DebugLogManager.LogType.EnemyAnimation);
            }
            
            // Update the animator
            if (AnimatorControlLookup.TryGetComponent(animatorRef.ValueRO.AnimatorEntity, out var animatorControl))
            {
                if (animatorControl.animatorInfo.animationID != (int)EnemyAnimationIDs.Dead)
                {
                    animatorControl.animatorInfo.animationID = (int)EnemyAnimationIDs.Dead;
                    AnimatorControlLookup[animatorRef.ValueRO.AnimatorEntity] = animatorControl;
                }
            }
        }
        
        // Handle normal animation updates for non-dead entities
        var animationJob = new AnimationUpdateJob
        {
            AnimatorControlLookup = AnimatorControlLookup,
            CurrentTime = (float)Time.ElapsedTime,
            ShouldLog = shouldLog
        };
        
        Dependency = animationJob.Schedule(Dependency);
    }

    [WithAll(typeof(EnemyTag))]
    [WithNone(typeof(DeadTag), typeof(HitTag))]
    private partial struct AnimationUpdateJob : IJobEntity
    {
        [NativeDisableParallelForRestriction]
        public ComponentLookup<AnimatorControlComponent> AnimatorControlLookup;
        public float CurrentTime;
        public bool ShouldLog;

        public void Execute(
            Entity entity,
            ref CharacterMovementState movementState,
            in CharacterSettings settings,
            in CharacterAnimatorReference animatorRef,
            in AgentBody agentBody,
            in LocalTransform transform,
            in EnemyTag enemyTag)
        {
            // Skip if animator entity is invalid
            if (animatorRef.AnimatorEntity == Entity.Null)
                return;

            // Calculate movement speed
            float speed = math.length(agentBody.Velocity);
            bool isMoving = speed > settings.MoveThreshold;
            
            // Determine the target animation ID
            int targetAnimationID;
            string animationName;
            
            if (movementState.IsAttacking)
            {
                // Check if attack animation should end
                if (CurrentTime - movementState.AttackStartTime >= movementState.AttackDuration)
                {
                    movementState.IsAttacking = false;
                }
                else
                {
                    targetAnimationID = (int)EnemyAnimationIDs.Attack;
                    animationName = "Attack";
                    
                    // Skip further processing
                    if (movementState.CurrentAnimationID == targetAnimationID)
                        return;
                }
            }
            
            // If not attacking or attack ended, determine movement animation
            if (!movementState.IsAttacking)
            {
                if (isMoving)
                {
                    if (speed > 2.0f)
                    {
                        targetAnimationID = (int)EnemyAnimationIDs.Run;
                        animationName = "Run";
                    }
                    else
                    {
                        targetAnimationID = (int)EnemyAnimationIDs.Walk;
                        animationName = "Walk";
                    }
                }
                else
                {
                    targetAnimationID = (int)EnemyAnimationIDs.Idle;
                    animationName = "Idle";
                }
            }
            
            // Update movement state
            movementState.IsMoving = isMoving;
            movementState.MoveSpeed = speed;
            
            // If the animation needs to change, update the animator
            if (targetAnimationID != movementState.CurrentAnimationID)
            {
                // Get the animator control component
                if (AnimatorControlLookup.TryGetComponent(animatorRef.AnimatorEntity, out var animatorControl))
                {
                    // Check if the target animation ID is valid
                    if (targetAnimationID >= 0)
                    {
                        // Log the animation change
                        if (ShouldLog)
                        {
                            Debug.Log($"[EnemyAnimationSystem] Entity {entity.Index} changing animation from {movementState.CurrentAnimationID} to {targetAnimationID} ({animationName})");
                        }
                        
                        // Update the animator control
                        animatorControl.animatorInfo.animationID = targetAnimationID;
                        AnimatorControlLookup[animatorRef.AnimatorEntity] = animatorControl;
                        
                        // Update the movement state
                        movementState.CurrentAnimationID = targetAnimationID;
                    }
                }
            }
        }
    }
}
```

### HitAnimationSystem

The `HitAnimationSystem` handles hit reaction animations when entities take damage.

```csharp
[UpdateAfter(typeof(DamageSystem))]
[UpdateBefore(typeof(EnemyAnimationSystem))]
public partial class HitAnimationSystem : SystemBase
{
    private EntityCommandBufferSystem m_EndSimulationEcbSystem;

    protected override void OnCreate()
    {
        m_EndSimulationEcbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
        RequireForUpdate<HitTag>();
    }

    protected override void OnUpdate()
    {
        var ecb = m_EndSimulationEcbSystem.CreateCommandBuffer();
        float deltaTime = Time.DeltaTime;
        bool loggingEnabled = DebugLogManager.Instance.IsLogTypeEnabled(DebugLogManager.LogType.EnemyAnimation);

        // Handle newly hit entities - process hit animation
        Entities
            .WithNone<HitAnimationStartedTag, DeadTag>()
            .WithAll<HitTag>()
            .ForEach((Entity entity,
                     ref CharacterMovementState movementState,
                     ref HitTag hitTag,
                     in CharacterAnimatorReference animatorRef) =>
            {
                // Skip if animator entity is invalid
                if (animatorRef.AnimatorEntity == Entity.Null)
                    return;
                
                // Set animation to hit reaction
                movementState.CurrentAnimationID = (int)EnemyAnimationIDs.Hit;
                
                // Mark hit animation as started
                ecb.AddComponent<HitAnimationStartedTag>(entity);
                
                // Set hit start time
                hitTag.HitTime = (float)Time.ElapsedTime;
                hitTag.HitDuration = 0.5f; // 0.5 seconds hit animation
                
                if (loggingEnabled)
                {
                    DebugLogManager.Instance.Log($"[HitAnimationSystem] Started hit animation for entity {entity.Index}", DebugLogManager.LogType.EnemyAnimation);
                }
            })
            .WithoutBurst() // Can't use Burst with DebugLogManager
            .Run();
            
        // Update hit animations in progress
        Entities
            .WithAll<HitAnimationStartedTag, HitTag>()
            .ForEach((Entity entity,
                     in HitTag hitTag) =>
            {
                // Check if hit animation should end
                float hitElapsedTime = (float)Time.ElapsedTime - hitTag.HitTime;
                if (hitElapsedTime >= hitTag.HitDuration)
                {
                    // Remove hit tags
                    ecb.RemoveComponent<HitTag>(entity);
                    ecb.RemoveComponent<HitAnimationStartedTag>(entity);
                    
                    if (loggingEnabled)
                    {
                        DebugLogManager.Instance.Log($"[HitAnimationSystem] Finished hit animation for entity {entity.Index}", DebugLogManager.LogType.EnemyAnimation);
                    }
                }
            })
            .WithoutBurst() // Can't use Burst with DebugLogManager
            .Run();
    }
}
```

### DeathAnimationSystem

The `DeathAnimationSystem` handles death animations when entities die. See the [Death Systems](#death-systems) section in the Health and Damage documentation for details.

## Animation Integration

### GPU ECS Animation Integration

The project uses GPU-based animation for high performance with large numbers of characters. This is integrated with the ECS architecture through the following components:

```csharp
// GPU ECS Animator component
public struct GPUECSAnimatorComponent : IComponentData
{
    public int AnimationID;
    public float AnimationSpeed;
    public float AnimationTime;
    public bool Loop;
}

// Animation culling component
public struct AnimationCullingComponent : IComponentData
{
    public float CullingDistance;
    public bool IsCulled;
}
```

The animation systems interact with the GPU animation system through these components, setting animation IDs and parameters.

### Rukhanka Animation System Integration

The project also supports integration with the Rukhanka Animation System, which provides additional features for animation blending and state machines.

```csharp
// Rukhanka animation systems bootstrap
public class RukhankaSystemsBootstrap : MonoBehaviour
{
    private void Awake()
    {
        var world = World.DefaultGameObjectInjectionWorld;
        
        // Add client animator controller systems
        var sysGroup = world.GetOrCreateSystemManaged<RukhankaAnimationSystemGroup>();
        var acs = world.CreateSystem<AnimatorControllerSystem<AnimatorControllerQuery>>();
        var facs = world.CreateSystem<FillAnimationsFromControllerSystem>();
        var aps = world.CreateSystem<AnimationProcessSystem>();
        var aas = world.CreateSystem<AnimationApplicationSystem>();
        var cs = world.CreateSystem<AnimationCullingSystem>();
        var ikGroup = world.GetOrCreateSystemManaged<RukhankaAnimationInjectionSystemGroup>();
        var actxus = world.GetOrCreateSystemManaged<AnimationCullingContextUpdateSystem>();
    }
}
```

## Special Animation Effects

### Hit Reactions

Hit reactions are triggered when an entity takes damage:

1. The `DamageSystem` adds a `HitTag` to the entity
2. The `HitAnimationSystem` detects the tag and sets the animation to the hit reaction
3. After the hit animation duration, the system removes the tags

### Death Animations

Death animations are triggered when an entity's health reaches zero:

1. The `DamageSystem` adds a `DeadTag` to the entity
2. The `DeathAnimationSystem` detects the tag and sets the animation to the death animation
3. After the death animation, a dissolve effect starts
4. Once the dissolve effect completes, the entity is destroyed

### Attack Animations

Attack animations are triggered by the `EnemyAttackTriggerSystem`:

```csharp
[UpdateBefore(typeof(EnemyAnimationSystem))]
public partial struct EnemyAttackTriggerSystem : ISystem
{
    private Unity.Mathematics.Random m_Random;
    private float m_AttackCheckTimer;
    private const float ATTACK_CHECK_INTERVAL = 1.0f;
    private const float ATTACK_CHANCE = 0.1f;
    private const float ATTACK_DURATION = 1.5f;
    
    public void OnUpdate(ref SystemState state)
    {
        // Update timer
        m_AttackCheckTimer += state.WorldUnmanaged.Time.DeltaTime;
        
        // Check if it's time to potentially trigger attacks
        if (m_AttackCheckTimer >= ATTACK_CHECK_INTERVAL)
        {
            m_AttackCheckTimer = 0;
            
            // Get current time
            float currentTime = (float)state.WorldUnmanaged.Time.ElapsedTime;
            
            // Create a job to randomly trigger attacks
            new TriggerAttackJob
            {
                Random = m_Random,
                AttackChance = ATTACK_CHANCE,
                AttackDuration = ATTACK_DURATION,
                CurrentTime = currentTime
            }.Schedule();
            
            // Update random seed
            m_Random = Unity.Mathematics.Random.CreateFromIndex((uint)currentTime);
        }
    }
    
    [WithAll(typeof(EnemyTag))]
    [WithNone(typeof(DeadTag), typeof(HitTag))]
    private partial struct TriggerAttackJob : IJobEntity
    {
        public Unity.Mathematics.Random Random;
        public float AttackChance;
        public float AttackDuration;
        public float CurrentTime;
        
        public void Execute(ref CharacterMovementState movementState)
        {
            // Skip if already attacking
            if (movementState.IsAttacking)
                return;
                
            // Random chance to start attack
            if (Random.NextFloat() < AttackChance)
            {
                movementState.IsAttacking = true;
                movementState.AttackStartTime = CurrentTime;
                movementState.AttackDuration = AttackDuration;
            }
        }
    }
}
```

## Implementation Guidelines

When implementing or modifying animation systems, follow these guidelines:

1. **Animation State Management**:
   - Use clear animation IDs for different states
   - Implement smooth transitions between animations
   - Handle priority for overlapping animations (e.g., hit reactions override movement)
   - Use animation events for triggering effects or sounds

2. **Performance Considerations**:
   - Use GPU-based animation for large numbers of characters
   - Implement animation culling for off-screen entities
   - Limit animation updates to visible entities
   - Use Burst compilation where possible

3. **Animation Blending**:
   - Implement smooth blending between animations
   - Use animation curves for natural transitions
   - Consider using animation layers for upper and lower body
   - Implement additive animations for subtle effects

4. **Special Effects**:
   - Integrate animations with particle effects
   - Synchronize animations with sound effects
   - Add camera shake or other feedback for impactful animations
   - Implement ragdoll physics for death animations

5. **Animation Configuration**:
   - Use ScriptableObjects for animation parameters
   - Centralize animation durations and thresholds
   - Make animation speeds adjustable
   - Allow for different animation sets based on entity type

6. **Integration with Other Systems**:
   - Coordinate with the targeting system for aiming animations
   - Integrate with the weapon system for firing animations
   - Synchronize with the movement system for locomotion
   - Coordinate with the damage system for hit reactions
