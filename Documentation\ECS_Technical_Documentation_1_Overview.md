# ECS Technical Documentation - Part 1: Overview

## Introduction

This document provides technical documentation for the Entity Component System (ECS) architecture used in the project. It is intended for developers who need to understand, maintain, or extend the ECS-based systems.

## Table of Contents

1. [ECS Architecture Overview](#ecs-architecture-overview)
2. [Core Components](#core-components)
3. [Main Systems](#main-systems)
4. [System Groups](#system-groups)
5. [Performance Considerations](#performance-considerations)

## ECS Architecture Overview

The project uses Unity's Data-Oriented Technology Stack (DOTS) with Entity Component System (ECS) to achieve high performance with large numbers of entities. The architecture follows these principles:

- **Components**: Pure data containers with no behavior
- **Systems**: Logic that operates on components
- **Entities**: Lightweight identifiers that components are attached to
- **Aspects**: Convenient views of related components on an entity
- **Jobs**: Parallelized work that operates on components
- **Burst Compilation**: Ahead-of-time compilation for high-performance code

The project is organized into several key subsystems that handle different aspects of gameplay, such as detection, targeting, shooting, health, animation, and enemy AI.

## Core Components

Components in ECS are pure data containers. Here are the core component types used in the project:

### Tags

Tags are empty components used to mark entities with specific properties:

```csharp
// Player tag
public struct PlayerTag : IComponentData { }

// Enemy tag
public struct EnemyTag : IComponentData { }

// Detection tags
public struct DetectedTag : IComponentData { }
public struct InFOVTag : IComponentData { }
public struct CurrentTargetTag : IComponentData { }
public struct UndetectableTag : IComponentData { }

// Health-related tags
public struct DeadTag : IComponentData { }
public struct HitTag : IComponentData { }
```

### Data Components

Data components store actual data:

```csharp
// Health component
public struct HealthComponent : IComponentData
{
    public float MaxHealth;
    public float CurrentHealth;
}

// Detection sensor component
public struct SphereDetectSensorComponent : IComponentData
{
    public bool CanDetect;
    public bool CanLose;
    public float CooldownTime;
    public float3 LastPosition;
    public int EntityIndex;
    public Entity LastDetectedEntity;
    public float3 SensorPosition;
    public float DetectionRadius;
    public float DetectionRange;
    public float DetectionAngle;
    public uint BelongsTo;
    public uint CollidesWith;
    public float CheckInterval;
    public float LastCheckTime;
    public Entity ClosestOutFOVTarget;
    public bool UseKDTree;
}

// Spawn component
public struct SpawnComponent : IComponentData
{
    public float SpawnTime;
}

// Enemy spawn component
public struct EnemySpawnComponent : IComponentData
{
    public Entity enemyPrefab;
    public float spawnInterval;
    public float spawnRadius;
    public float playerAvoidRadius;
    public int spawnBatchSize;
    public int maxEnemyCount;
    public int currentEnemyCount;
    public float timeSinceLastSpawn;
}
```

### Buffer Components

Buffer components store collections of data:

```csharp
// Buffer for detected enemies
public struct DetectedEnemyBuffer : IBufferElementData
{
    public Entity Entity;
    public float3 Position;
    public bool IsInFOV;
    public float Score;
}

// Buffer for body part positions
public struct BodyPartPositionBuffer : IBufferElementData
{
    public float3 Position;
    public Entity Entity;
    public int PartType;
}
```

## Main Systems

Systems in ECS contain the logic that operates on components. Here are the main system categories:

### Detection and Targeting Systems

- **OptimizedDetectionSystem**: Detects enemies within the player's field of view
- **AimingModule**: Selects the best target from detected enemies
- **TriadClusterSystem**: Groups nearby enemies into clusters for better targeting

### Weapon and Shooting Systems

- **OptimizedShootingSystem**: Handles weapon firing and bullet creation
- **WeaponAimSystem**: Manages weapon aiming and rotation
- **WeaponDetectionSettingsSystem**: Updates detection settings based on weapon config
- **SensorUpdateSystem**: Updates sensor parameters based on weapon settings

### Health and Damage Systems

- **DamageSystem**: Applies damage to entities with health components
- **HealthBarSpawnSystem**: Creates and updates health bars for entities
- **DeathAnimationSystem**: Handles death animations and effects

### Animation Systems

- **EnemyAnimationSystem**: Manages enemy animations based on state
- **HitAnimationSystem**: Plays hit animations when enemies take damage
- **DeathAnimationSystem**: Plays death animations when enemies die

### Enemy Spawning and AI Systems

- **OptimizedSpawnerSystem**: Spawns enemies at configured intervals
- **SpawnerSystem**: Alternative spawner using Crowds navigation
- **EnemyAttackTriggerSystem**: Triggers enemy attack animations

### Spatial Partitioning Systems

- **KDTreeSystem**: Manages a KD-tree for spatial queries
- **SpatialHashGridSystem**: Alternative spatial partitioning system

## System Groups

Systems are organized into groups to control update order:

```csharp
// Health systems group
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class HealthSystemsGroup : ComponentSystemGroup
{
    protected override void OnCreate()
    {
        base.OnCreate();
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<HealthBarSystemsGroup>());
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<DamageSystem>());
    }
}

// Animation systems group
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(HealthSystemsGroup))]
public partial class AnimationSystemsGroup : ComponentSystemGroup
{
    protected override void OnCreate()
    {
        base.OnCreate();
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<EnemyAnimationSystem>());
        AddSystemToUpdateList(World.GetOrCreateSystemManaged<HitAnimationSystem>());
    }
}
```

## Performance Considerations

The project extensively uses Unity's Data-Oriented Technology Stack (DOTS) and Burst compilation for high performance:

1. **Entity Component System (ECS)**:
   - Components are pure data, enabling efficient memory layout
   - Systems operate on components in a data-oriented manner
   - Entities are lightweight identifiers, reducing memory overhead

2. **Burst Compilation**:
   - Critical systems are Burst-compiled for high performance
   - Jobs are used for parallel processing of entities
   - Native containers are used to avoid garbage collection

3. **Job System**:
   - Parallel processing of entities using the C# Job System
   - Dependency management for efficient job scheduling
   - Batch processing for better cache utilization

4. **Spatial Partitioning**:
   - KD-tree and spatial hash grid for efficient spatial queries
   - Optimized for finding nearby entities quickly
   - Reduces the need for expensive distance calculations

5. **Animation Optimization**:
   - GPU-based animation for large numbers of characters
   - Animation culling for off-screen entities
   - Efficient animation state management
