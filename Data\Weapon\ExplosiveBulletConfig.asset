%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 546374d2430cb1e4aaa47c3441db500a, type: 3}
  m_Name: ExplosiveBulletConfig
  m_EditorClassIdentifier: 
  bulletName: Explosive Bullet
  bulletType: 4
  bulletPrefab: {fileID: 0}
  baseStats:
    speed: 15
    damageMultiplier: 1.2
    maxDistance: 70
    canPenetrate: 0
    maxPenetrations: 0
    penetrationDamageReduction: 0.3
    explodeOnImpact: 1
    explosionRadius: 3
    explosionDamageMultiplier: 0.7
    impactEffectPrefab: {fileID: 0}
    trailEffectPrefab: {fileID: 0}
  upgradeLevels:
  - levelName: Enhanced Explosive Bullet
    upgradeCost: 200
    stats:
      speed: 16
      damageMultiplier: 1.3
      maxDistance: 75
      canPenetrate: 0
      maxPenetrations: 0
      penetrationDamageReduction: 0.3
      explodeOnImpact: 1
      explosionRadius: 4
      explosionDamageMultiplier: 0.75
      impactEffectPrefab: {fileID: 0}
      trailEffectPrefab: {fileID: 0}
  - levelName: Advanced Explosive Bullet
    upgradeCost: 400
    stats:
      speed: 17
      damageMultiplier: 1.5
      maxDistance: 80
      canPenetrate: 0
      maxPenetrations: 0
      penetrationDamageReduction: 0.3
      explodeOnImpact: 1
      explosionRadius: 5
      explosionDamageMultiplier: 0.8
      impactEffectPrefab: {fileID: 0}
      trailEffectPrefab: {fileID: 0}
